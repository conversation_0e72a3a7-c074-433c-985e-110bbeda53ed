# 技术架构

## 整体架构

AB测试调度器采用了分层架构设计，主要包括以下几个部分：

1. **HTTP接口层**：处理外部请求，提供RESTful API
2. **业务逻辑层**：实现核心业务逻辑，如分流算法、用户状态管理等
3. **数据访问层**：负责与各种数据存储系统交互
4. **基础设施层**：提供配置管理、日志、监控等基础功能

## 技术栈

1. **编程语言**：
   - Go语言

2. **数据存储**：
   - Redis：用于缓存实验配置和用户状态
   - MongoDB：用于持久化存储用户状态
   - MySQL：存储实验配置的主数据源

3. **消息队列**：
   - Kafka：用于异步处理用户状态更新和日志记录

4. **依赖注入**：
   - 使用Google Wire和自定义的autowire工具进行依赖注入

5. **Web框架**：
   - 使用Gin作为HTTP服务框架

6. **监控和日志**：
   - Prometheus：用于指标收集
   - Sentry：用于错误跟踪和监控

## 代码结构

```
- httpd                      # HTTP 接口适配，不要放业务逻辑
- service                    # 业务逻辑
  - data_transfer.go         # 同步数据 admin-mysql -> redis
  - config_meta_store.go     # 实验原始数据维护
  - config_service.go        # 从 Mem/Redis 读取实验配置
  - user_state.go            # 用户状态维护：分流流程和重定向模块读写
  - admin_service.go         # 管理后台相关：需要改写后台状态的都收在这里
  - redirect_service.go      # 冻结重定向模块
- resource                   # 资源文件，编译时会被打包到二进制里
- gopkg                      # 公共类库
- infra                      # 项目基础设施
```

## 部署架构

系统支持多种部署方式：

1. **单机部署**：
   - 使用Systemd管理服务
   - 适用于开发和测试环境

2. **容器化部署**：
   - 支持Docker容器化
   - 可在Kubernetes集群中部署

3. **高可用部署**：
   - 多实例部署，通过负载均衡分发请求
   - Redis集群提供高可用缓存服务
   - MongoDB副本集确保数据持久化的可靠性

## 数据流

1. **配置同步流**：
   - 管理后台MySQL -> Redis缓存 -> 内存缓存
   - 定时任务触发同步，确保配置最新

2. **分流请求流**：
   - 客户端请求 -> HTTP接口 -> 分流逻辑 -> 返回结果
   - 同时异步更新用户状态

3. **用户状态流**：
   - 分流结果 -> Redis缓存 -> Kafka消息队列 -> MongoDB/MySQL持久化

## 扩展性设计

1. **算法扩展**：
   - 通过工厂模式支持新的分流算法

2. **存储扩展**：
   - 抽象存储接口，支持不同的存储后端

3. **功能扩展**：
   - 模块化设计，便于添加新功能模块
