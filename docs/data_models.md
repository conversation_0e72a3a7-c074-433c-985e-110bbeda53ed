# 核心数据模型

AB测试调度器系统中的核心数据模型定义了实验配置和用户状态的结构。以下是主要的数据模型及其关系。

## 项目(Project)

项目是实验的顶层容器，代表一个产品或功能的实验集合。

```go
type ProjectIdx struct {
    ID               int                             // 项目ID
    Key              string                          // 项目key，客户端使用的标识
    Layers           []int                           // 普通层IDs
    LayerExclusiveID int                             // 独占实验层ID
    GroupParamStyle  logic.GroupParamStyle           // 实验组参数样式
    ConflictRule     adminmodel.FeatureConflictRules // 冲突列表
    Domains          []*Domain                       // 域
}
```

## 域(Domain)

域是项目内的流量分区，用于控制不同层的流量分配。

```go
type Domain struct {
    LayerIDs []LayerID // 层IDs
    ModRight int32     // mod 右区间，开区间
}
```

## 层(Layer)

层是实验的组织单位，可以包含多个实验。层有优先级属性，用于处理实验冲突。

```go
type LayerIdx struct {
    ID            int            // 层ID
    Name          string         // 名称
    Level         int32          // 层优先级
    Exclusive     bool           // 是否独占层
    Prop          int16          // 独占层流量比例
    UserWhiteList map[string]int // 用户ID => 实验组ID
    DivType       int8           // 分流类型：1:比例 2:权重
    Mod           int32          // 分流hash模数
    ModKey        []string       // 分流hash入参：uid,did
    BucketStr     string         // 桶状态字符串
    PrjID         int            // 项目ID
    ExpIDs        []int          // 实验IDs
    GroupIDs      []int          // 实验组IDs，仅包含运行中的组
    CachedAt      time.Time      // 当前缓存的更新时间
    Seed          string         // 随机种子
    Buckets       []Bucket       // 桶
}
```

## 桶(Bucket)

桶是哈希分流的基本单位，每个桶可以分配给一个实验组。

```go
type Bucket struct {
    State   int8 // 状态：0:空白 2:预订中 3:使用中
    GroupId int  // 实验组ID
    ExpId   int  // 实验ID
}
```

## 实验(Exp)

实验定义了一组相关的测试方案，包含多个实验组。

```go
type Exp struct {
    ID              int                 // 实验ID
    Status          int8                // 状态：1:调试 2:灰度 3:运行 4:冻结 5:停止
    Prop            int16               // 流量比例，权重
    Algo            int8                // 分流算法：1:hash 2:普通轮询 3:CSR
    CSR             []*CSRConfig        // csr配置
    ActiveUType     int8                // 允许用户类型：1:新用户 2:老用户 3:所有用户
    Strategy        adminmodel.Strategy // 策略
    RunningGroupIds []int               // 运行中的实验组IDs
    AllGroupIds     []int               // 所有实验组IDs，包含冻结
    PrjID           int                 // 项目ID
    LayerID         int                 // 实验层ID
    UserDuration    time.Duration       // 用户停留时长，多长时间后自动自动退组
    StartTime       *time.Time          // 开始时间
    RateLimit       *RateLimit          // 限流
}
```

## 实验组(Group)

实验组是实验中的具体方案，包含参数配置和特性信息。

```go
type Group struct {
    ID              int                   // 实验组ID
    Key             string                // 实验组key：140-31 (Schema)
    Status          int8                  // 状态：1:空白 2:冻结 3:使用中
    GroupParamStyle logic.GroupParamStyle // 实验组参数样式
    ParamJSON       string                // 参数
    ExpID           int                   // 实验ID
    PrjID           int                   // 项目ID
    LayerID         int                   // 实验层ID
    GroupName       string                // 实验组名称
    RedirectTo      []*SplitGroup         // 重定向到的实验组
    FeatureKeys     []string              // 特征key
    FeatureIDs      []string              // 特征ID
}
```

## 用户状态(UserState)

用户状态记录了用户在各实验中的分配情况。

```go
type UserState struct {
    ID       int       // 记录ID
    UID      string    // 用户ID
    GrpId    int       // 实验组ID
    LayerId  int       // 层ID
    ExpId    int       // 实验ID
    EnterAt  time.Time // 进入时间
    ExpireAt time.Time // 过期时间
    Rand     int       // 随机数
}
```

## 分流上下文(HitContext)

分流上下文用于在分流过程中传递信息。

```go
type HitContext struct {
    Layer           *model.LayerIdx // 当前层信息
    HitState        *HitState       // 命中信息维护，包含读出的缓存，更新的结果
    Hit             bool            // 是否命中
    Group           *model.Group    // 命中方案元信息
    GroupParam      map[string]any  // 命中方案参数，相比 group.Param 去掉了冲突的 key
    Exp             *model.Exp      // 命中实验元信息
    LDebug          *model.LDebug   // 调试信息
    WhiteCombGrpIds []int           // 组合白名单信息
}
```

## 数据模型关系

数据模型之间的关系如下：

1. **项目(Project)** 包含多个 **层(Layer)**
2. **层(Layer)** 包含多个 **实验(Exp)**
3. **实验(Exp)** 包含多个 **实验组(Group)**
4. **层(Layer)** 包含多个 **桶(Bucket)**，每个桶可以分配给一个实验组
5. **用户状态(UserState)** 记录用户在特定层中分配到的实验组

这种层次结构使得系统能够灵活地管理复杂的实验配置，并高效地进行用户分流。
