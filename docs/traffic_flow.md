# 分流流程

本文档详细描述了AB测试调度器的分流流程，即如何将用户分配到不同的实验组。

## 整体流程

分流过程主要由`service/abtest/distribute.go`中的`Traffic`方法实现，整体流程如下：

1. 接收分流请求，包含项目标识、用户ID和用户属性
2. 获取项目配置
3. 获取用户当前状态
4. 构建用户标签
5. 执行主分流逻辑
6. 处理重定向
7. 更新用户状态
8. 返回分流结果

## 详细步骤

### 1. 接收分流请求

分流请求包含以下主要信息：

```go
type Request struct {
    ProjectKey string         // 项目标识
    Uid        string         // 用户ID
    DeviceID   string         // 设备ID
    BundleId   string         // 包ID
    Attributes map[string]any // 用户属性
    Debug      int32          // 调试标志
}
```

### 2. 获取项目配置

根据`ProjectKey`从配置服务获取项目信息：

```go
project, err := ab.ConfigService.GetProject(ctx, 0, request.ProjectKey)
```

### 3. 获取用户当前状态

从用户状态服务获取用户在该项目中的当前状态：

```go
originStates, err := ab.UserStateService.Gets(ctx, project.ID, request.Uid)
```

### 4. 构建用户标签

根据请求中的用户属性和当前状态构建用户标签：

```go
userLabels, err := ab.UserLabelService.BuildUserLabels(ctx, request.Uid, request.Attributes, originStates)
```

### 5. 执行主分流逻辑

主分流逻辑由`handleInDomain`方法实现，主要步骤如下：

#### 5.1 确定用户所属域

```go
domain := project.PickDomain(ctxs.UserId(ctx))
```

#### 5.2 获取域内所有层

```go
layers, err := ab.ConfigService.GetLayers(ctx, domain.LayerIDs)
```

#### 5.3 获取用户白名单组合

```go
whiteComb, err := ab.WhiteCombService.Get(ctx, project.Key, uid, did)
```

#### 5.4 按优先级分组处理各层

```go
leveldLayers := lo.GroupBy(layers, func(layer *m.LayerIdx) int32 { return layer.Level })
```

#### 5.5 对每个层执行分流

对于每个层，执行`handleLayer`方法：

```go
func (ab *DistributeService) handleLayer(ctx context.Context, userLabels map[string]any, hitCtx *service.HitContext) {
    // 执行层内分流逻辑
    group, hitCache, csrIndentity, err := ab.execLayer(ctx, userLabels, hitCtx)
    if err != nil || group == nil {
        return
    }
    
    // 设置命中信息
    hitCtx.Group = group
    hitCtx.Exp, _ = ab.GetExp(ctx, group.ExpID)
    
    // 频控检查
    if !hitCache {
        if !ab.RateService.PassLimit(ctx, hitCtx.Exp, group) {
            hitCtx.HitState.Exit()
            hitCtx.LDebug.RateLimit = true
            return
        }
    }
    
    // 标记命中
    hitCtx.Hit = true
    hitCtx.GroupParam = parseGroupParamJSON(group.ParamJSON)
    hitCtx.HitState.CsrIndentity = csrIndentity
}
```

### 6. 层内分流逻辑

层内分流由`execLayer`方法实现，主要步骤如下：

#### 6.1 检查白名单

```go
if group, ok := ab.getWhite(ctx, tryGIDs); ok {
    ldebug.HitWhite = true
    return group, false, "", nil
}
```

#### 6.2 检查用户缓存

```go
if state != nil && state.GroupID != 0 {
    ldebug.HitCache = true
    if group, err = ab.checkGroupState(ctx, state, int(now.Unix())); err == nil {
        exp, err := ab.GetExp(ctx, group.ExpID)
        if err == nil {
            if exp.CanEnter(uLabel.Get, true) {
                return group, true, state.CsrIndentity, nil
            }
        }
    }
    // 用户因为触发条件不满足而退组
    ldebug.LeaveCache = state.GroupID
    state.Exit()
}
```

#### 6.3 哈希分桶

```go
index := ab.HashService.HashUserInLayer(uid, layer)
buckets := layer.GetBuckets()
bucket := buckets[index]
```

#### 6.4 检查实验进入条件

```go
exp, err := ab.GetExp(ctx, bucket.ExpId)
if !exp.CanEnter(uLabel.Get, false) {
    hitCtx.LDebug.TriggerFailed = true
    return nil, false, csrIndentity, nil
}
```

#### 6.5 根据算法选择实验组

根据实验的算法类型，选择相应的分组方法：

```go
switch exp.Algo {
case m.ExpAlgoHash:
    groupId = bucket.GroupId
case adminmodel.ExpAlgoRoundRobin:
    groupId, err = ab.execRoundRobin(layer.PrjID, layer.ID, exp.ID, exp.RunningGroupIds)
case adminmodel.ExpAlgoCSR:
    groupId, csrIndentity, err = ab.execCSR(ctx, layer.PrjID, layer.ID, exp.ID, exp.CSR, exp.RunningGroupIds, userLabels)
}
```

### 7. 处理冲突

在所有层处理完成后，处理不同优先级层之间的冲突：

```go
resultCtxs = ab.removeConflict(ctx, project, resultCtxs)
```

### 8. 更新用户状态

根据分流结果更新用户状态：

```go
err = ab.UserStateService.Update(ctx, project.ID, request.Uid, prjState, originStates)
```

### 9. 返回分流结果

构建并返回分流结果：

```go
resp = &Response{
    HitSample:    HitSample{ExpIds: expIds, GrpIds: grpIds},
    Params:       params,
    Hits:         hits,
    Debug:        debug,
    CsrIndentity: csrIndentity,
}
```

## 分流算法

系统支持多种分流算法：

### 1. 哈希分流

基于用户ID对桶数取模，将用户固定分配到特定桶。

```go
func (h *HashService) HashUserInLayer(uid string, layer *model.LayerIdx) (bucketIdx int) {
    seed := strconv.Itoa(layer.ID)
    if extra := layer.GetSeed(); extra != "" {
        seed += ":" + extra
    }
    return int(h.Hash(seed, uid, uint64(layer.Mod)))
}
```

### 2. 轮询分流

按顺序轮流分配用户到不同实验组。

```go
func (rr *RoundRobin) Next() Scheme {
    scheme := rr.schemes[rr.currentIndex]
    rr.currentIndex = (rr.currentIndex + 1) % rr.schemeCount
    return scheme
}
```

### 3. CSR分流

基于用户属性进行分层路由，将具有相似属性的用户分配到同一实验组。

```go
func (ab *DistributeService) execCSR(ctx context.Context, prjId, layerId, expId int, csrConfigs []*m.CSRConfig, groupIds []int, userLabels map[string]any) (group int, csrIndentity string, err error) {
    indentityConfigs, keys, err := convertCSRConfigs(csrConfigs)
    if err != nil {
        return 0, "", err
    }
    
    key := buildSudokuRoundRobinId(prjId, layerId, expId)
    csrIndentity, err = csr.ParseCsrIndentityFromCsrParams(csrConfig, userLabels)
    key += ":" + csrIndentity
    
    schemes := buildSchemesFromGroups(groupIds)
    csrAlgo, err := algo.NewFactory().NewAlgo(algo.TypeSodokuRoundRobin, key, schemes)
    groupId := csrAlgo.Next().ID
    return groupId, csrIndentity, err
}
```

## 总结

AB测试调度器的分流流程是一个复杂而精细的过程，涉及多个步骤和多种算法。通过这一流程，系统能够根据配置将用户准确地分配到不同的实验组，同时处理各种边缘情况和冲突。
