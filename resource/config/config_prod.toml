addr = ":7020"
mode = "prod"

[sentry]
env = "prod"
dsn = "http://a170683951dfb59658a561a52cf7f3d0@106.75.65.198:9000/3"
traces_sample_rate = 0.00001

[database]
mysql_default = "abuser"

[database.mysql.abuser]
dsn = "abtest_user_slt:Wqk4581rGlE1wRaa@tcp(rds-abtest-user-2504.cluster-cjmimqdxiiih.us-east-2.rds.amazonaws.com:3306)/abtest_user?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"

[database.mysql.admindb]
dsn = "abtest_backend_slt:CJy9p+lh7s4Wc6PG@tcp(10.9.119.14:3306)/abtest_backend?charset=utf8mb4&parseTime=True&loc=Asia%2fShanghai&timeout=3s&readTimeout=5s"
debug = true

[database.mysql.admindb.read]
dsn = "abtest_backend_slt:CJy9p+lh7s4Wc6PG@tcp(sjpt-rds-abtest.cluster-ro-cjmimqdxiiih.us-east-2.rds.amazonaws.com:3306)/abtest_backend?charset=utf8mb4&parseTime=True&loc=Asia%2fShanghai&timeout=3s&readTimeout=5s"
debug = true

[database.redis.default]
addr = "sjpt-abtest-redis.cye3w9.clustercfg.use2.cache.amazonaws.com:6379"
cluster = true

[database.redis.default_v2]
addr = "abtest-base-valkey.cye3w9.ng.0001.use2.cache.amazonaws.com:6379"
db = 1

[database.redis.ab_config]	# 实验配置
addr = "abtest-base-valkey.cye3w9.ng.0001.use2.cache.amazonaws.com:6379"
db = 10

[database.redis.user_state]	# 用户分桶状态
addr = "sjpt-abtest-redis.cye3w9.clustercfg.use2.cache.amazonaws.com:6379"
cluster = true



[database.redis.user_label]	# 用户标签
addr = "sjpt-abtest-redis.cye3w9.clustercfg.use2.cache.amazonaws.com:6379"
cluster = true

[database.redis.admin]	# 给 admin 后台使用的数据
addr = "106.75.61.33:16379"
password = "z3juFwyduLkP3Tsr"
db = 4

[database.mongodb.abtest]
auth = "aws"
secret_name = "rds!cluster-65138c5a-3044-4db0-9eca-026dc0c32b86"
uri = "mongodb://%s:%<EMAIL>:27017/?tls=true&tlsCAFile=/data/service/keys/global-bundle.pem&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
db = "abtest"

[mq.kafka.abtest]
addrs = ["b-2.abtestkafkaprod0109.ymyegv.c3.kafka.us-east-2.amazonaws.com:9094","b-3.abtestkafkaprod0109.ymyegv.c3.kafka.us-east-2.amazonaws.com:9094","b-1.abtestkafkaprod0109.ymyegv.c3.kafka.us-east-2.amazonaws.com:9094"]
ssl = true
