addr = ":7020"
mode = "dev"

[sentry]
env = "dev"
dsn = "http://a170683951dfb59658a561a52cf7f3d0@***********:9000/3"
traces_sample_rate = 0.01

[database]
mysql_default = "abuser"

#
# dev为云上环境，要填内网IP
#

[database.mysql.abuser]
dsn = "xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(***********:3306)/abtest_user?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.mysql.admindb]
dsn = "xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(***********:3306)/abtest_dev?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.mysql.admindb.read]
dsn = "xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(***********:3306)/abtest_dev?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.redis.default]
addr = "***********:6379"
db = 4

[database.redis.default_v2]
addr = "***********:6379"
db = 4

[database.redis.ab_config]	# 实验配置
addr = "***********:6379"
db = 11

[database.redis.user_state]	# 用户分桶状态
addr = "***********:6666"
db = 6

[database.redis.user_label]	# 用户标签
addr = "***********:6379"
db = 7

[database.redis.admin]	# 给 admin 后台使用的数据
addr = "***********:6379"
db = 4

[database.mongodb.abtest]
uri = "**************************************************************************"
db = "abtest"

[database.mongodb.abtest_unit_testing]
uri = "**************************************************************************"
db = "abtest_unit_testing"

[mq.kafka.abtest]
addrs = ["***********:9092"]
