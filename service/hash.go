package service

import (
	"strconv"

	"git.7k7k.com/data/abScheduler/model"
	"github.com/spaolacci/murmur3"
)

var ()

// @autowire(set=service)
type HashService struct{}

func (h *HashService) HashUserInLayer(uid string, layer *model.LayerIdx) (bucketIdx int) {
	salt := strconv.Itoa(layer.ID)
	if extra := layer.GetSeed(); extra != "" {
		salt += ":" + extra
	}

	return int(h.Hash(salt, uid, uint64(layer.Mod)))
}

func (h *HashService) Hash(prefixSalt string, uid string, mod uint64) uint64 {
	key := murmur3.Sum64([]byte(prefixSalt + ":" + uid))
	return key % mod
}
