package service

import (
	"context"
	"fmt"
	"log/slog"
	"maps"
	"math/rand/v2"
	"os"
	"sort"
	"sync"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/getsentry/sentry-go"

	"git.7k7k.com/data/abScheduler/infra/redises"
	m "git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/pkg/common/gosentry"
	"git.7k7k.com/pkg/common/notifier"
	"git.7k7k.com/pkg/storage"
	"git.7k7k.com/pkg/storage/codec"
	"github.com/panjf2000/ants/v2"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
)

// ConfigService
// 在线分流时使用的实验配置信息
type ConfigService struct {
	ProjectByID  storage.CacheKV[int, *m.ProjectIdx]
	ProjectByKey storage.CacheKV[string, *m.ProjectIdx]
	LayerIdx     storage.CacheKV[int, *m.LayerIdx]
	ExpIdx       storage.CacheKV[int, *m.Exp]
	GroupIdx     storage.CacheKV[int, *m.Group]

	NotifyService *NotifyService
}

// @autowire(set=service)
func NewConfigService(clts *redises.ClientMgr, notifyService *NotifyService) *ConfigService {
	s := &ConfigService{
		NotifyService: notifyService,
	}

	// sizePrj := int(unsafe.Sizeof(m.ProjectIdx{})) * 100
	// sizeLayer := int(unsafe.Sizeof(m.LayerIdx{})) * 1000
	// sizeExp := int(unsafe.Sizeof(m.Exp{})) * 2000
	// sizeGroup := int(unsafe.Sizeof(m.Group{})) * 10000

	memTime := time.Hour * 24
	redisTime := time.Hour * 24 * 30

	version := "1"

	s.ProjectByID = storage.NewProxy(
		storage.NewGoCacheAdapter[int, *m.ProjectIdx](memTime, memTime),
		// storage.NewFreeCacheAdapter[int, *m.ProjectIdx](sizePrj, codec.SonicCodec[*m.ProjectIdx]{}, memTime),
		storage.NewRedisAdapter(clts.ExpConfigRedis, codec.SonicCodec[*m.ProjectIdx]{}, func(id int) string {
			return fmt.Sprintf("ab:project{P}:%s:%d", version, id)
		}, redisTime),
	).WithMetric("config_project")
	s.ProjectByKey = storage.NewProxy(
		storage.NewGoCacheAdapter[string, *m.ProjectIdx](memTime, memTime),
		// storage.NewFreeCacheAdapter[string, *m.ProjectIdx](sizePrj, codec.SonicCodec[*m.ProjectIdx]{}, memTime),
		storage.NewRedisAdapter(clts.ExpConfigRedis, codec.SonicCodec[*m.ProjectIdx]{}, func(key string) string {
			return fmt.Sprintf("ab:projectKey{P}:%s:%s", version, key)
		}, redisTime),
	).WithMetric("config_project_key")
	s.LayerIdx = storage.NewProxy(
		storage.NewGoCacheAdapter[int, *m.LayerIdx](memTime, memTime),
		// storage.NewFreeCacheAdapter[int, *m.LayerIdx](sizeLayer, codec.SonicCodec[*m.LayerIdx]{}, memTime),
		storage.NewRedisAdapter(clts.ExpConfigRedis, codec.SonicCodec[*m.LayerIdx]{}, func(id int) string {
			return fmt.Sprintf("ab:layer{L}:%s:%d", version, id)
		}, redisTime),
	).WithMetric("config_layer")
	s.ExpIdx = storage.NewProxy(
		storage.NewGoCacheAdapter[int, *m.Exp](memTime, memTime),
		// storage.NewFreeCacheAdapter[int, *m.Exp](sizeExp, codec.SonicCodec[*m.Exp]{}, memTime),
		storage.NewRedisAdapter(clts.ExpConfigRedis, codec.SonicCodec[*m.Exp]{}, func(id int) string {
			return fmt.Sprintf("ab:exp{E}:%s:%d", version, id)
		}, redisTime),
	).WithMetric("config_exp")
	s.GroupIdx = storage.NewProxy(
		storage.NewGoCacheAdapter[int, *m.Group](memTime, memTime),
		// storage.NewFreeCacheAdapter[int, *m.Group](sizeGroup, codec.SonicCodec[*m.Group]{}, memTime),
		storage.NewRedisAdapter(clts.ExpConfigRedis, codec.SonicCodec[*m.Group]{}, func(id int) string {
			return fmt.Sprintf("ab:group{G}:%s:%d", version, id)
		}, redisTime),
	).WithMetric("config_group")

	go notifyService.ExpNotifier.Listen(context.Background(), s.listenNotify)
	return s
}

func (s *ConfigService) GetProject(ctx context.Context, projectID int, key string) (project *m.ProjectIdx, err error) {
	if projectID > 0 {
		return s.ProjectByID.Get(ctx, projectID)
	}

	return s.ProjectByKey.Get(ctx, key)
}

func (s *ConfigService) GetLayers(ctx context.Context, layerIDs []int) (layers []*m.LayerIdx, err error) {
	_, finish := gosentry.StartSpan(ctx, "cache.get_item", "", "GetLayers")
	defer finish()

	layerMap, err := s.LayerIdx.MGet(ctx, layerIDs)
	if err != nil {
		return nil, err
	}

	layers = lo.Values(layerMap)
	sort.Slice(layers, func(i, j int) bool {
		return layers[i].SortBy() < layers[j].SortBy()
	})

	return
}

func (s *ConfigService) GetExps(ctx context.Context, expIDs []int) (exps map[int]*m.Exp, err error) {
	_, finish := gosentry.StartSpan(ctx, "cache.get_item", "", "GetExps")
	defer finish()

	return s.ExpIdx.MGet(ctx, expIDs)
}

func (s *ConfigService) GetGroup(ctx context.Context, groupIDs []int) (groups map[int]*m.Group, err error) {
	return s.GroupIdx.MGet(ctx, groupIDs)
}

// DeleteExps 删除实验，包括 mem+redis
func (s *ConfigService) DeleteExps(ctx context.Context, expIDs []int) (err error) {
	exps, err := s.GetExps(ctx, expIDs)
	if err != nil {
		return err
	}

	grpIds := []int{}
	for _, exp := range exps {
		grpIds = append(grpIds, exp.AllGroupIds...)
	}

	err = s.ExpIdx.(*storage.Proxy[int, *m.Exp]).Backup().(storage.CacheKV[int, *m.Exp]).MDel(ctx, expIDs)
	if err != nil {
		return err
	}

	err = s.ExpIdx.MDel(ctx, expIDs)
	if err != nil {
		return err
	}

	err = s.GroupIdx.(*storage.Proxy[int, *m.Group]).Backup().(storage.CacheKV[int, *m.Group]).MDel(ctx, grpIds)
	if err != nil {
		return err
	}

	err = s.GroupIdx.MDel(ctx, grpIds)
	if err != nil {
		return err
	}

	return
}

type NotifyMsg struct {
	Projects    []int    `json:"projects,omitempty"`
	ProjectKeys []string `json:"project_keys,omitempty"`
	Layers      []int    `json:"layers,omitempty"`
	Exps        []int    `json:"exps,omitempty"`
	Groups      []int    `json:"groups,omitempty"`
}

type ctxKeyUpdate struct{}

// updateIndexToRedis
// 1. 更新 bucket 状态
// 2. 通知其他 POD 更新
// TODO 版本号
func (s *ConfigService) updateIndexToRedis(ctx context.Context, index *ConfigIndex) {
	span0, finish0 := gosentry.StartSpan(ctx, "config.updateIndexToRedis", "", "updateIndexToRedis")
	defer finish0()

	span := span0.StartChild("cache.set", sentry.WithDescription("project"))
	for _, project := range index.ProjectsByID {
		s.ProjectByID.Set(ctx, project.ID, project)
		s.ProjectByKey.Set(ctx, project.Key, project)
	}
	span.Finish()

	span = span0.StartChild("cache.set", sentry.WithDescription("exp"))
	for _, exp := range index.Exps {
		s.ExpIdx.Set(ctx, exp.ID, exp)
	}
	span.Finish()

	span = span0.StartChild("cache.set", sentry.WithDescription("group"))
	groups := make(map[int]*m.Group, len(index.Groups))
	for _, group := range index.Groups {
		groups[group.ID] = group
	}
	s.GroupIdx.MSet(ctx, groups)
	span.Finish()

	// 更新 bucket 状态
	updatedLayers := s.updateLayerIdxWithBucket(ctx, index.PrjLayers)

	// 通知其他 Pod 更新本地缓存
	s.NotifyService.ExpNotifier.Send(ctx, NotifyMsg{
		Projects:    lo.Keys(index.ProjectsByID),
		ProjectKeys: lo.Keys(index.ProjectsByKey),
		Layers:      updatedLayers,
		Exps:        lo.Keys(index.Exps),
		Groups:      lo.Keys(index.Groups),
	})
}

// updateLayerIdxWithBucket 最重要的分桶逻辑
// 1. 计算 bucket 状态
// 2. 写入 Redis
func (s *ConfigService) updateLayerIdxWithBucket(ctx context.Context, prjLayers map[int]map[int]*m.LayerIdx) (updatedLayers []int) {
	span0, finish0 := gosentry.StartSpan(ctx, "config.updateLayerIdxWithBucket", "", "updateLayerIdxWithBucket")
	defer finish0()

	lock := sync.Mutex{}

	p, _ := ants.NewPool(8)

	wg := sync.WaitGroup{}
	for _, layers := range prjLayers {
		for _, _l := range layers {
			wg.Add(1)
			layerNew := _l
			_ = p.Submit(func() {
				defer wg.Done()

				_, finish := gosentry.StartChild(span0, "config.calc_bucket", sentry.WithDescription(layerNew.Name))
				defer finish()

				layerId := layerNew.ID

				// 1. 初始化：取现有的bucket状态
				newBuckets := make([]m.Bucket, layerNew.Mod)
				layerOrigin, err := s.LayerIdx.Get(ctx, layerId)
				if err != nil && !errors.Is(err, redis.Nil) {
					slog.ErrorContext(ctx, "get layer from redis", "layer_id", layerId, "error", err.Error())
					return
				}
				if layerOrigin == nil { // 新层
					layerOrigin = &m.LayerIdx{}
				}

				// 2. 初始化桶的状态
				groupCntCurrent := map[int]int{} // 当前桶状态
				groupCntExpect := map[int]int{}  // 期望桶状态

				// 2.1 迁移还在使用的实验和实验组
				{
					newGroupIDSet := lo.SliceToMap(layerNew.GroupIDs, func(groupId int) (int, bool) { return groupId, true })
					for i, bucket := range layerOrigin.GetBuckets() {
						if !bucket.IsEmpty() && newGroupIDSet[bucket.GroupId] { // 当前 group 还可用
							g, _ := s.GroupIdx.Get(ctx, bucket.GroupId)
							if g.NeedBucket() { // 但未冻结
								newBuckets[i] = bucket
								groupCntCurrent[bucket.GroupId]++
							}
						}
					}
				}

				// 2.2 计算每个 group 期望的桶：计算每个实验期望的桶，然后均分到每个实验组
				{
					for _, expId := range layerNew.ExpIDs {
						exp, err := s.ExpIdx.Get(ctx, expId)
						if err != nil {
							slog.ErrorContext(ctx, "get exp from redis", "exp_id", expId, "error", err.Error())
							continue
						}
						expCnt := int(exp.Prop) * int(layerNew.Mod) / 100
						for _, grpId := range exp.RunningGroupIds {
							groupCntExpect[grpId] = expCnt / len(exp.RunningGroupIds)
						}
					}
				}

				// 3. 根据期望状态更新桶状态
				for grpId, expect := range groupCntExpect {
					g, _ := s.GroupIdx.Get(ctx, grpId)
					exp, err := s.ExpIdx.Get(ctx, g.ExpID)
					if err != nil {
						slog.ErrorContext(ctx, "get exp from redis", "exp_id", g.ExpID, "error", err.Error())
						continue
					}
					needAdd := expect - groupCntCurrent[grpId]
					if needAdd > 0 {
						if exp.IsGray() { // 灰度发布
							rate := exp.IntimeGrayRate(time.Now())             // 计算当前时间下需要分配的比例
							expectNow := expect * rate / 100                   // 当前时间下需要分配的桶
							needAdd = max(expectNow-groupCntCurrent[grpId], 0) // 最少0个桶，不能减
							slog.InfoContext(ctx, "gray_state", "exp_id", g.ExpID, "grp_id", grpId, "expect", expect, "expect_now", expectNow, "current_rate", fmt.Sprint(groupCntCurrent[grpId]*100/expect, "%"), "next_rate", fmt.Sprint(rate, "%"), "need_add", needAdd)
						}
						s.bucketGrowForGrp(newBuckets, needAdd, g.ExpID, grpId, m.BucketStateUsing)
					} else if needAdd < 0 {
						s.bucketShrinkForGrp(newBuckets, -needAdd, g.ExpID, grpId)
					}
				}

				layerNew.SetBuckets(newBuckets)
				nochange := layerOrigin.BucketStr == layerNew.BucketStr &&
					maps.Equal(layerOrigin.UserWhiteList, layerNew.UserWhiteList)
				expired := layerNew.CachedAt.Sub(layerOrigin.CachedAt).Minutes() > 10
				needUpdate := !nochange || expired || os.Getenv("FORCE_UPDATE_REDIS") == "1" || ctx.Value(ctxKeyUpdate{}) == 1
				if needUpdate {
					err := s.LayerIdx.Set(ctx, layerId, layerNew)
					if err != nil {
						slog.ErrorContext(ctx, "set_layer_to_redis", "layer_id", layerId, "error", err.Error())
					}

					lock.Lock()
					updatedLayers = append(updatedLayers, layerId)
					lock.Unlock()
				}
			})
		}
	}
	wg.Wait()

	// for _, l := range prjLayers[1] {
	// dump.Dump(l.DebugState())
	// }

	return
}

// bucketGrowForGrp 在 buckets 里随机分配 grow 个桶
// - buckets: 当前桶状态
// - grow: 需要分配的桶数量
// 算法过程：先把 bucket 里的空桶打乱，按乱序后的顺序进行分配
func (s *ConfigService) bucketGrowForGrp(buckets []m.Bucket, grow int, expId int, grpId int, state int8) {
	// 收集所有空桶的索引
	emptyIndices := make([]int, 0, len(buckets))
	for i := range buckets {
		if buckets[i].IsEmpty() {
			emptyIndices = append(emptyIndices, i)
		}
	}

	if len(emptyIndices) < grow {
		slog.Error("bucketGrowForGrpMax", "grow", grow, "expId", expId, "grpId", grpId, "state", state, "available", len(emptyIndices))
		grow = len(emptyIndices)
	}

	// 打乱所有空桶的索引
	lo.Shuffle(emptyIndices)

	// 使用打乱后的前 grow 个索引进行分配
	for i := 0; i < grow; i++ {
		idx := emptyIndices[i]
		buckets[idx].State = state
		buckets[idx].ExpId = expId
		buckets[idx].GroupId = grpId
	}
}

// bucketShrinkForGrp 回收桶
func (s *ConfigService) bucketShrinkForGrp(buckets []m.Bucket, shrink int, expId int, grpId int) {
	offset := rand.IntN(len(buckets))
	for shrink > 0 {
		offset = (offset + 1) % len(buckets)
		if !buckets[offset].IsEmpty() &&
			buckets[offset].ExpId == expId &&
			buckets[offset].GroupId == grpId {

			buckets[offset].Clear()
			shrink--
		}
	}
}

// listenNotify 接受配置变更消息，更新本地缓存
func (s *ConfigService) listenNotify(ctx context.Context, rawMsg notifier.Message[NotifyMsg]) error {
	if len(rawMsg.Payload.Projects) > 0 {
		err := s.ProjectByID.MDel(ctx, rawMsg.Payload.Projects)
		if err != nil {
			return err
		}
		_, _ = s.ProjectByID.MGet(ctx, rawMsg.Payload.Projects)
	}

	if len(rawMsg.Payload.ProjectKeys) > 0 {
		err := s.ProjectByKey.MDel(ctx, rawMsg.Payload.ProjectKeys)
		if err != nil {
			return err
		}
		_, _ = s.ProjectByKey.MGet(ctx, rawMsg.Payload.ProjectKeys)
	}

	if len(rawMsg.Payload.Layers) > 0 {
		err := s.LayerIdx.MDel(ctx, rawMsg.Payload.Layers)
		if err != nil {
			return err
		}
		_, _ = s.LayerIdx.MGet(ctx, rawMsg.Payload.Layers)
	}

	if len(rawMsg.Payload.Exps) > 0 {
		err := s.ExpIdx.MDel(ctx, rawMsg.Payload.Exps)
		if err != nil {
			return err
		}
		_, _ = s.ExpIdx.MGet(ctx, rawMsg.Payload.Exps)
	}

	if len(rawMsg.Payload.Groups) > 0 {
		err := s.GroupIdx.MDel(ctx, rawMsg.Payload.Groups)
		if err != nil {
			return err
		}
		_, _ = s.GroupIdx.MGet(ctx, rawMsg.Payload.Groups)
	}

	return nil
}
