package service

import (
	"fmt"
	"time"
	"unsafe"

	model "git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/pkg/storage"
	"git.7k7k.com/pkg/storage/codec"
)

// ConfigMetaStore
// 原始数据，跟后台的表结构保持一致
type ConfigMetaStore struct {
	Project    storage.RepoKV[int64, *model.Project]
	Layer      storage.RepoKV[int64, *model.Layer]
	Exp        storage.RepoKV[int64, *model.Exp]
	Group      storage.RepoKV[int64, *model.Group]
	SplitGroup storage.RepoKV[int64, *model.SplitGroup]
}

// @autowire(set=service)
func NewConfigMetaStore(clts *redises.ClientMgr) *ConfigMetaStore {
	r := &ConfigMetaStore{}

	sizeProject := int(unsafe.Sizeof(model.Project{})) * 100
	sizeLayer := int(unsafe.Sizeof(model.Layer{})) * 1000
	sizeExp := int(unsafe.Sizeof(model.Exp{})) * 2000
	sizeGroup := int(unsafe.Sizeof(model.Group{})) * 10000

	memTime := time.Hour * 3
	redisTime := time.Hour * 24 * 3

	r.Project = storage.NewProxy(
		storage.NewFreeCacheAdapter[int64](sizeProject, codec.SonicCodec[*model.Project]{}, memTime),
		storage.NewRedisAdapter(clts.ExpConfigRedis, codec.SonicCodec[*model.Project]{}, func(id int64) string {
			return fmt.Sprintf("meta:prj:%d", id)
		}, redisTime),
	).WithMetric("meta_project")
	r.Layer = storage.NewProxy(
		storage.NewFreeCacheAdapter[int64](sizeLayer, codec.SonicCodec[*model.Layer]{}, memTime),
		storage.NewRedisAdapter(clts.ExpConfigRedis, codec.SonicCodec[*model.Layer]{}, func(id int64) string {
			return fmt.Sprintf("meta:layer:%d", id)
		}, redisTime),
	).WithMetric("meta_layer")
	r.Exp = storage.NewProxy(
		storage.NewFreeCacheAdapter[int64](sizeExp, codec.SonicCodec[*model.Exp]{}, memTime),
		storage.NewRedisAdapter(clts.ExpConfigRedis, codec.SonicCodec[*model.Exp]{}, func(id int64) string {
			return fmt.Sprintf("meta:exp:%d", id)
		}, redisTime),
	).WithMetric("meta_exp")
	r.Group = storage.NewProxy(
		storage.NewFreeCacheAdapter[int64](sizeGroup, codec.SonicCodec[*model.Group]{}, memTime),
		storage.NewRedisAdapter(clts.ExpConfigRedis, codec.SonicCodec[*model.Group]{}, func(id int64) string {
			return fmt.Sprintf("meta:group:%d", id)
		}, redisTime),
	).WithMetric("meta_group")
	r.SplitGroup = storage.NewProxy(
		storage.NewFreeCacheAdapter[int64](sizeGroup, codec.SonicCodec[*model.SplitGroup]{}, memTime),
		storage.NewRedisAdapter(clts.ExpConfigRedis, codec.SonicCodec[*model.SplitGroup]{}, func(id int64) string {
			return fmt.Sprintf("meta:split_group:%d", id)
		}, redisTime),
	)
	return r
}
