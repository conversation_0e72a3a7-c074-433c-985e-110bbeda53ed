package service

import (
	"sync"
	"testing"

	"git.7k7k.com/data/abAdmin/gopkg/condition"
	adminmodel "git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abScheduler/infra/config"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/data/abScheduler/repository/mongos"
	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
)

// 添加测试用的 mock 数据结构
type mockData struct {
	projects []*adminmodel.Project
	layers   []*adminmodel.Layer
	exps     []*adminmodel.Exp
	groups   []*adminmodel.Group
}

// createMockData
// 测试数据结构说明：
// Project1 (ID: 1, Name: "project1")
// ├── Layer1 (ID: 1, Name: "p1_layer1", 独占层)
// │   ├── Exp1 (ID: 1, Name: "p1_layer1_exp1", 流量: 50%)
// │   │   ├── Group1 (ID: 1, Name: "p1_layer1_exp1_g1", 白名单: user1,user2)
// │   │   ├── Group2 (ID: 2, Name: "p1_layer1_exp1_g2", 白名单: user3,user4)
// │   │   └── Group3 (ID: 3, Name: "p1_layer1_exp1_g3", 白名单: user5,user6)
// │   │
// │   └── Exp2 (ID: 2, Name: "p1_layer1_exp2", 流量: 30%)
// │       ├── Group1 (ID: 4, Name: "p1_layer1_exp2_g1", 白名单: user7,user8)
// │       └── Group2 (ID: 5, Name: "p1_layer1_exp2_g2", 白名单: user9,user10)
// │
// └── Layer2 (ID: 2, Name: "p1_layer2", 非独占层)
//
//	└── Exp1 (ID: 3, Name: "p1_layer2_exp1", 流量: 40%)
//	    ├── Group1 (ID: 6, Name: "p1_layer2_exp1_g1", 白名单: user11,user12)
//	    └── Group2 (ID: 7, Name: "p1_layer2_exp1_g2", 白名单: user13,user14)
//
// Project2 (ID: 2, Name: "project2")
// └── (无关联实验)
// 特点说明：
// 1. Layer1 是独占层(IsExclusion=1)，包含2个实验
// 2. Layer2 是非独占层(IsExclusion=0)，包含1个实验
// 3. 每个实验都处于运行状态(ExpStatusRunning)
// 4. 每个实验组都有独立的参数配置和白名单用户
func createMockData() mockData {
	return mockData{
		projects: []*adminmodel.Project{
			{
				ID:       1,
				UniqueID: "project1",
				State:    1,
			},
			{
				ID:       2,
				UniqueID: "project2",
				State:    1,
			},
		},
		layers: []*adminmodel.Layer{
			{
				ID:          1,
				ProjectID:   1,
				Name:        "p1_layer1",
				IsExclusion: 1,
				Rate:        50,
			},
			{
				ID:          2,
				ProjectID:   1,
				Name:        "p1_layer2",
				IsExclusion: 0,
				Rate:        100,
			},
		},
		exps: []*adminmodel.Exp{
			{
				ID:        1,
				Name:      "p1_layer1_exp1",
				ProjectID: 1,
				LayerID:   1,
				State:     int32(adminmodel.ExpStatusRunning),
				LayerRate: 50,

				Strategy: &adminmodel.Strategy{},
				Csr: []*adminmodel.CSRData{
					{
						Key:  "uid",
						Type: condition.ValueType(""),
					},
				},
			},
			{
				ID:        2,
				Name:      "p1_layer1_exp2",
				ProjectID: 1,
				LayerID:   1,
				State:     int32(adminmodel.ExpStatusRunning),
				LayerRate: 30,
				Strategy:  &adminmodel.Strategy{},
				Csr: []*adminmodel.CSRData{
					{
						Key:  "uid",
						Type: condition.ValueType(""),
					},
				},
			},
			{
				ID:        3,
				Name:      "p1_layer2_exp1",
				ProjectID: 1,
				LayerID:   2,
				State:     int32(adminmodel.ExpStatusRunning),
				LayerRate: 40,
				Strategy:  &adminmodel.Strategy{},
				Csr: []*adminmodel.CSRData{
					{
						Key:  "uid",
						Type: condition.ValueType(""),
					},
				},
			},
		},
		groups: []*adminmodel.Group{
			{
				ID:            1,
				ExpID:         1,
				Name:          "p1_layer1_exp1_g1",
				State:         adminmodel.GroupStateRunning,
				ParamsContent: `{"key":"value1"}`,
				WhiteList:     "user1,user2",
			},
			{
				ID:            2,
				ExpID:         1,
				Name:          "p1_layer1_exp1_g2",
				State:         adminmodel.GroupStateRunning,
				ParamsContent: `{"key":"value2"}`,
				WhiteList:     "user3,user4",
			},
			{
				ID:            3,
				ExpID:         1,
				Name:          "p1_layer1_exp1_g3",
				State:         adminmodel.GroupStateRunning,
				ParamsContent: `{"key":"value3"}`,
				WhiteList:     "user5,user6",
			},
			{
				ID:            4,
				ExpID:         2,
				Name:          "p1_layer1_exp2_g1",
				State:         adminmodel.GroupStateRunning,
				ParamsContent: `{"key":"value4"}`,
				WhiteList:     "user7,user8",
			},
			{
				ID:            5,
				ExpID:         2,
				Name:          "p1_layer1_exp2_g2",
				State:         adminmodel.GroupStateRunning,
				ParamsContent: `{"key":"value5"}`,
				WhiteList:     "user9,user10",
			},
			{
				ID:            6,
				ExpID:         3,
				Name:          "p1_layer2_exp1_g1",
				State:         adminmodel.GroupStateRunning,
				ParamsContent: `{"key":"value6"}`,
				WhiteList:     "user11,user12",
			},
			{
				ID:            7,
				ExpID:         3,
				Name:          "p1_layer2_exp1_g2",
				State:         adminmodel.GroupStateRunning,
				ParamsContent: `{"key":"value7"}`,
				WhiteList:     "user13,user14",
			},
		},
	}
}

func setupMongo(_ *testing.T) *mongos.MongoSet {
	c := config.Config{}
	c.DataBase.MongoDB = map[string]config.Mongo{
		"abtest": {
			URI: "****************************************************************************",
			DB:  "abtest_unit_testing",
		},
	}
	return mongos.NewMongos(&c)
}

func TestMain(m *testing.M) {
	setupDataTransfer()
	createMockData()
	m.Run()
}

func MustNil(err error) {
	if err != nil {
		panic(err)
	}
}

var (
	setupRedisOnce   sync.Once
	testingRedisMr   *miniredis.Miniredis
	testingRedis     redis.UniversalClient
	testingRedisClts *redises.ClientMgr
)

func setupRedis(t *testing.T) (*redises.ClientMgr, redis.UniversalClient, *miniredis.Miniredis) {
	setupRedisOnce.Do(func() {
		mr := miniredis.NewMiniRedis()
		if err := mr.Start(); err != nil {
			t.Fatalf("could not start miniredis: %s", err)
		}
		rds := redis.NewUniversalClient(&redis.UniversalOptions{Addrs: []string{mr.Addr()}})
		testingRedis = rds
		testingRedisClts = redises.Use(rds)
		testingRedisMr = mr
	})
	return testingRedisClts, testingRedis, testingRedisMr
}
