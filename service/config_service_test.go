package service

import (
	"context"
	"strconv"
	"testing"

	m "git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/pkg/storage/codec"
	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupConfigService(t *testing.T) (*ConfigService, *miniredis.Miniredis) {
	clts, _, mr := setupRedis(t)

	notifyService := NewNotifyService(clts)

	service := NewConfigService(clts, notifyService)

	// 使用 mockdata
	mock := createMockData()

	// 设置项目数据
	for _, project := range mock.projects {
		prjIdx := &m.ProjectIdx{
			ID:  int(project.ID),
			Key: project.UniqueID,
		}
		value, err := codec.Default.MarshalToString(prjIdx)
		require.NoError(t, err)
		mr.Set("ab:project{P}:1:"+strconv.Itoa(int(project.ID)), value)
		mr.Set("ab:projectKey{P}:1:"+project.UniqueID, value)
	}

	// 设置层数据
	for _, layer := range mock.layers {
		layerIdx := &m.LayerIdx{
			ID:        int(layer.ID),
			Name:      layer.Name,
			Exclusive: layer.IsExclusion == 1,
		}
		value, err := codec.Default.MarshalToString(layerIdx)
		require.NoError(t, err)
		mr.Set("ab:layer{L}:1:"+strconv.Itoa(int(layer.ID)), value)
	}

	// 设置实验数据
	for _, exp := range mock.exps {
		expIdx := &m.Exp{
			ID:     int(exp.ID),
			Status: int8(exp.State),
			Prop:   int16(exp.LayerRate),
		}
		value, err := codec.Default.MarshalToString(expIdx)
		require.NoError(t, err)
		mr.Set("ab:exp{E}:1:"+strconv.Itoa(int(exp.ID)), value)
	}

	// 设置分组数据
	for _, group := range mock.groups {
		groupIdx := &m.Group{
			ID:        int(group.ID),
			Key:       group.Name,
			Status:    int8(group.State),
			ParamJSON: group.ParamsContent,
			ExpID:     int(group.ExpID),
		}
		value, err := codec.Default.MarshalToString(groupIdx)
		require.NoError(t, err)
		mr.Set("ab:group{G}:1:"+strconv.Itoa(int(group.ID)), value)
	}

	return service, mr
}

func TestConfigService_GetProject(t *testing.T) {
	service, _ := setupConfigService(t)
	mock := createMockData()

	tests := []struct {
		name      string
		projectID int
		key       string
		project   *m.ProjectIdx
		wantErr   bool
	}{
		{
			name:      "通过ID获取项目",
			projectID: int(mock.projects[0].ID),
			key:       "",
			project: &m.ProjectIdx{
				ID:  int(mock.projects[0].ID),
				Key: mock.projects[0].UniqueID,
			},
		},
		{
			name:      "通过Key获取项目",
			projectID: 0,
			key:       mock.projects[0].UniqueID,
			project: &m.ProjectIdx{
				ID:  int(mock.projects[0].ID),
				Key: mock.projects[0].UniqueID,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()

			got, err := service.GetProject(ctx, tt.projectID, tt.key)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.project.ID, got.ID)
			assert.Equal(t, tt.project.Key, got.Key)
		})
	}
}

func TestConfigService_GetLayers(t *testing.T) {
	service, _ := setupConfigService(t)
	mock := createMockData()

	tests := []struct {
		name     string
		layerIDs []int
		layers   []*m.LayerIdx
		wantErr  bool
	}{
		{
			name:     "获取多个层",
			layerIDs: []int{int(mock.layers[0].ID), int(mock.layers[1].ID)},
			layers: []*m.LayerIdx{
				{
					ID:        int(mock.layers[0].ID),
					Name:      mock.layers[0].Name,
					Exclusive: mock.layers[0].IsExclusion == 1,
				},
				{
					ID:        int(mock.layers[1].ID),
					Name:      mock.layers[1].Name,
					Exclusive: mock.layers[1].IsExclusion == 0,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()

			got, err := service.GetLayers(ctx, tt.layerIDs)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, len(tt.layers), len(got))

			// 验证排序(独占层在前)
			if len(got) > 1 {
				assert.True(t, got[0].Exclusive)
			}
		})
	}
}

func TestConfigService_updateLayerIdxWithBucket(t *testing.T) {
	service, mr := setupConfigService(t)

	ctx := context.Background()

	// 设置实验数据
	exp1 := &m.Exp{
		ID:              1,
		Status:          1,
		Prop:            50, // layer1_exp1 占50%流量
		RunningGroupIds: []int{1, 2, 3},
	}
	exp2 := &m.Exp{
		ID:              2,
		Status:          1,
		Prop:            30, // layer1_exp2 占30%流
		RunningGroupIds: []int{4, 5},
	}
	exp3 := &m.Exp{
		ID:              3,
		Status:          1,
		Prop:            40, // layer2_exp1 占40%流量
		RunningGroupIds: []int{6, 7},
	}

	// 将实验数据存入Redis
	for _, exp := range []*m.Exp{exp1, exp2, exp3} {
		value, err := codec.Default.MarshalToString(exp)
		require.NoError(t, err)
		mr.Set("ab:exp{E}:1:"+strconv.Itoa(exp.ID), value)
	}

	// 设置层数据
	layer1 := &m.LayerIdx{
		ID:        1,
		Name:      "p1_layer1",
		Exclusive: true,
		ExpIDs:    []int{1, 2}, // layer1包含exp1和exp2
		GroupIDs:  []int{1, 2, 3, 4, 5},
		Mod:       200, // 200个桶
	}
	layer2 := &m.LayerIdx{
		ID:        2,
		Name:      "p1_layer2",
		Exclusive: false,
		ExpIDs:    []int{3}, // layer2包含exp3
		GroupIDs:  []int{6, 7},
		Mod:       200, // 200个桶
	}

	// 初始化空桶
	buckets1 := make([]m.Bucket, layer1.Mod)
	buckets2 := make([]m.Bucket, layer2.Mod)
	layer1.SetBuckets(buckets1)
	layer2.SetBuckets(buckets2)

	// 将层数据存入Redis
	for _, layer := range []*m.LayerIdx{layer1, layer2} {
		value, err := codec.Default.MarshalToString(layer)
		require.NoError(t, err)
		mr.Set("ab:layer{L}:1:"+strconv.Itoa(layer.ID), value)
	}

	prjLayers := map[int]map[int]*m.LayerIdx{
		1: {
			1: layer1,
			2: layer2,
		},
	}

	// 执行测试
	updatedLayers := service.updateLayerIdxWithBucket(ctx, prjLayers)

	// 验证结果
	assert.ElementsMatch(t, []int{1, 2}, updatedLayers)

	// 验证layer1的桶分配
	layer1After, err := service.LayerIdx.Get(ctx, 1)
	require.NoError(t, err)
	buckets := layer1After.GetBuckets()

	// 统计layer1中各个实验和分组的桶数
	expBuckets := make(map[int]int)   // 实验桶统计
	groupBuckets := make(map[int]int) // 分组桶统计
	emptyBuckets := 0                 // 空桶统计
	for _, b := range buckets {
		if b.IsEmpty() {
			emptyBuckets++
			continue
		}
		expBuckets[b.ExpId]++
		groupBuckets[b.GroupId]++
	}

	// 验证layer1的exp1(50%)
	// 由于有3个分组，每个分组33个桶(向下取整)，总共分配99个桶
	assert.Equal(t, 99, expBuckets[1], "exp1应该分配99个桶(3个分组各33个)")
	// 验证exp1的三个分组应该完全相同
	assert.Equal(t, 33, groupBuckets[1], "exp1的group1应该分配33个桶")
	assert.Equal(t, 33, groupBuckets[2], "exp1的group2应该分配33个桶")
	assert.Equal(t, 33, groupBuckets[3], "exp1的group3应该分配33个桶")

	// 验证layer1的exp2(30%)
	// 有2个分组，每个分组30个桶，总共分配60个桶
	assert.Equal(t, 60, expBuckets[2], "exp2应该分配30%的桶(60个)")
	// 验证exp2的两个分组该平均分配
	assert.Equal(t, 30, groupBuckets[4], "exp2的group1应该分配30个桶")
	assert.Equal(t, 30, groupBuckets[5], "exp2的group2应该分配30个桶")

	// 验证剩余的��应该是空的
	assert.Equal(t, 41, emptyBuckets, "应该有41个空桶(200-99-60)")

	// 验证layer2的桶分配
	layer2After, err := service.LayerIdx.Get(ctx, 2)
	require.NoError(t, err)
	buckets = layer2After.GetBuckets()

	// 重置统计
	expBuckets = make(map[int]int)
	groupBuckets = make(map[int]int)
	emptyBuckets = 0
	for _, b := range buckets {
		if b.IsEmpty() {
			emptyBuckets++
			continue
		}
		expBuckets[b.ExpId]++
		groupBuckets[b.GroupId]++
	}

	// 验证layer2的exp3(40%)应该分配到80个桶
	assert.Equal(t, 80, expBuckets[3], "exp3应该分配40%的桶(80个)")
	// 验证exp3的两个分组应该平均分配
	assert.Equal(t, 40, groupBuckets[6], "exp3的group1应该分配40个桶")
	assert.Equal(t, 40, groupBuckets[7], "exp3的group2应该分配40个桶")

	// 验证剩余的桶应该是空的
	assert.Equal(t, 120, emptyBuckets, "应该有120个空桶")

	// 验证layer1的桶使用数量
	assert.Equal(t, 159, layer1After.BucketUsed, "layer1应该使用159个桶(99+60)")

	// 验证layer2的桶使用数量
	assert.Equal(t, 80, layer2After.BucketUsed, "layer2应该使用80个桶")

	// 测试调整实验流量后的桶分配
	t.Run("调整实验流量后重新分配桶", func(t *testing.T) {
		// 只修改内存中的 exp1 数据,不更新 redis
		exp1.Prop = 60 // layer1_exp1 从50%调整到60%流量
		service.ExpIdx.Set(ctx, exp1.ID, exp1)

		// 重新调用 updateLayerIdxWithBucket
		updatedLayers := service.updateLayerIdxWithBucket(ctx, prjLayers)

		// 验证结果：只有 layer1 应该被更新，因为只修改了 layer1 中的实验
		assert.ElementsMatch(t, []int{1}, updatedLayers, "只有 layer1 应该被更新，因为只修改了 layer1 中的实验")

		// 验证layer1的桶分配
		layer1After, err := service.LayerIdx.Get(ctx, 1)
		require.NoError(t, err)
		buckets := layer1After.GetBuckets()

		// 重新统计各个实验和分组的桶数
		expBuckets := make(map[int]int)   // 实验桶统计
		groupBuckets := make(map[int]int) // 分组桶统计
		emptyBuckets := 0                 // 空桶统计
		for _, b := range buckets {
			if b.IsEmpty() {
				emptyBuckets++
				continue
			}
			expBuckets[b.ExpId]++
			groupBuckets[b.GroupId]++
		}

		// 验证layer1的exp1(60%)
		// 由于有3个分组，每个分组40个桶(向下取整)，总共分配120个桶
		assert.Equal(t, 120, expBuckets[1], "exp1应该分配120个桶(3个分组各40个)")
		// 验证exp1的三个分组应该完全相同
		assert.Equal(t, 40, groupBuckets[1], "exp1的group1应该分配40个桶")
		assert.Equal(t, 40, groupBuckets[2], "exp1的group2应该分配40个桶")
		assert.Equal(t, 40, groupBuckets[3], "exp1的group3应该分配40个桶")

		// 验证layer1的exp2(30%)保持不变
		assert.Equal(t, 60, expBuckets[2], "exp2应该保持30%的桶(60个)")
		assert.Equal(t, 30, groupBuckets[4], "exp2的group1应该保持30个桶")
		assert.Equal(t, 30, groupBuckets[5], "exp2的group2应该保持30个桶")

		// 验证剩余的桶应该是空的
		assert.Equal(t, 20, emptyBuckets, "应该有20个空桶(200-120-60)")

		// 验证layer1的桶使用数量
		assert.Equal(t, 180, layer1After.BucketUsed, "layer1应该使用180个桶(120+60)")
	})
}
