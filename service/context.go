package service

import (
	"git.7k7k.com/data/abAdmin/model/logic"
	"git.7k7k.com/data/abScheduler/model"
	set "github.com/deckarep/golang-set/v2"
)

type (
	// HitContext 分流时的上下文，过程数据都记录在这里，最后输出到Response
	HitContext struct {
		Layer           *model.LayerIdx // 当前层信息
		HitState        *HitState       // 命中信息维护，包含读出的缓存，更新的结果
		Hit             bool            // 是否命中
		Group           *model.Group    // 命中方案元信息
		GroupParam      map[string]any  // 命中方案参数，相比 group.Param 去掉了冲突的 key
		Exp             *model.Exp      // 命中实验元信息
		LDebug          *model.LDebug   // 调试信息
		WhiteCombGrpIds []int           // 组合白名单信息
	}

	LDebug = model.LDebug
)

// ConflictKeys 计算 grp 里不能使用的 key 和 id
func (hit *HitContext) ConflictKeys(project *model.ProjectIdx, grp *model.Group, usingFeatKeys set.Set[string], usingFeatIDs []string) (conflictKeys, conflictIDs []string) {
	conflictKeys = make([]string, 0, len(grp.FeatureKeys)+len(grp.FeatureIDs))

	{ // 校验冲突的 Key
		keys := grp.FeatureKeys
		for _, key := range keys {
			if usingFeatKeys.Contains(key) {
				conflictKeys = append(conflictKeys, key)
			}
		}
	}

	{ // 按照规则列表校验冲突的 ID
		ids := grp.FeatureIDs
		for _, id := range ids {
			for _, usingID := range usingFeatIDs {
				if logic.IsConflictFeatureID(id, usingID, project.ConflictRule.GetConflictIDs) {
					conflictIDs = append(conflictIDs, id)
				}
			}
		}
	}

	return
}
