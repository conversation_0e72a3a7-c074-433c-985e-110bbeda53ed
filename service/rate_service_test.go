package service

import (
	"context"
	"fmt"
	"testing"
	"time"

	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/data/abScheduler/model"
	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
)

func setupRateService(t *testing.T) (*RateService, *miniredis.Miniredis) {
	mr := miniredis.RunT(t)
	rdb := redis.NewClient(&redis.Options{Addr: mr.Addr()})
	service := NewRateService(redises.Use(rdb))
	return service, mr
}

func TestRateService_IncrBelowLimit(t *testing.T) {
	service, mr := setupRateService(t)
	ctx := context.Background()

	tests := []struct {
		name      string
		key       string
		limit     int
		ttl       time.Duration
		setupFunc func()
		wantPass  bool
	}{
		{
			name:  "首次访问，未超过限制",
			key:   "test_key_1",
			limit: 10,
			ttl:   time.Hour,
			setupFunc: func() {
				// 不需要设置初始值
			},
			wantPass: true,
		},
		{
			name:  "已有访问记录，未超过限制",
			key:   "test_key_2",
			limit: 10,
			ttl:   time.Hour,
			setupFunc: func() {
				mr.Set("rtlm:test_key_2", "5")
			},
			wantPass: true,
		},
		{
			name:  "已有访问记录，刚好达到限制",
			key:   "test_key_3",
			limit: 10,
			ttl:   time.Hour,
			setupFunc: func() {
				mr.Set("rtlm:test_key_3", "9")
			},
			wantPass: true,
		},
		{
			name:  "已有访问记录，超过限制",
			key:   "test_key_4",
			limit: 10,
			ttl:   time.Hour,
			setupFunc: func() {
				mr.Set("rtlm:test_key_4", "10")
			},
			wantPass: false,
		},
		{
			name:  "Redis 错误情况",
			key:   "test_key_5",
			limit: 10,
			ttl:   time.Hour,
			setupFunc: func() {
				mr.SetError("SIMULATED_ERROR")
			},
			wantPass: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置 Redis 状态
			mr.FlushAll()

			// 设置测试数据
			if tt.setupFunc != nil {
				tt.setupFunc()
			}

			// 执行测试
			result := service.incrBelowLimit(ctx, tt.key, tt.limit, tt.ttl)
			assert.Equal(t, tt.wantPass, result)

			// 验证 Redis 操作结果
			if tt.name != "Redis 错误情况" && result {
				// 检查计数器是否增加
				val, err := mr.Get("rtlm:" + tt.key)
				assert.NoError(t, err)
				assert.NotEmpty(t, val, "计数器值不应为空")

				// 检查 TTL 是否设置（仅对首次访问的情况）
				if tt.name == "首次访问，未超过限制" {
					ttl := mr.TTL("rtlm:" + tt.key)
					assert.Greater(t, ttl, 0*time.Second)
				}
			}
		})
	}
}

func TestRateService_IncrBelowLimit_Concurrency(t *testing.T) {
	service, mr := setupRateService(t)
	ctx := context.Background()

	// 测试并发情况下的限流
	key := "concurrent_test"
	limit := 5
	ttl := time.Hour

	// 重置 Redis 状态
	mr.FlushAll()

	// 并发调用 incrBelowLimit
	results := make([]bool, 10)
	for i := 0; i < 10; i++ {
		results[i] = service.incrBelowLimit(ctx, key, limit, ttl)
	}

	// 验证结果：前5个应该通过，后5个应该被限流
	passCount := 0
	for _, passed := range results {
		if passed {
			passCount++
		}
	}

	assert.Equal(t, limit, passCount, "应该只有 %d 个请求通过限流", limit)

	// 验证计数器值
	val, err := mr.Get("rtlm:" + key)
	assert.NoError(t, err)
	assert.Equal(t, "10", val, "计数器应该增加到10")
}

func TestRateService_PassLimit(t *testing.T) {
	service, mr := setupRateService(t)
	ctx := context.Background()

	// 创建测试用的 RateLimit 对象
	createRateLimit := func(limit int32, duration time.Duration) *model.RateLimit {
		return &model.RateLimit{
			Limit:    limit,
			Duration: duration,
		}
	}

	tests := []struct {
		name      string
		exp       *model.Exp
		group     *model.Group
		setupFunc func()
		wantPass  bool
	}{
		{
			name: "实验未设置限流",
			exp: &model.Exp{
				ID:        1,
				RateLimit: nil, // 未设置限流
			},
			group: &model.Group{
				ID: 101,
			},
			setupFunc: nil,
			wantPass:  true, // 应该直接通过
		},
		{
			name: "首次访问，未超过限制",
			exp: &model.Exp{
				ID:        2,
				RateLimit: createRateLimit(10, time.Hour), // 限流为10
			},
			group: &model.Group{
				ID: 102,
			},
			setupFunc: nil,
			wantPass:  true,
		},
		{
			name: "已有访问记录，未超过限制",
			exp: &model.Exp{
				ID:        3,
				RateLimit: createRateLimit(10, time.Hour),
			},
			group: &model.Group{
				ID: 103,
			},
			setupFunc: func() {
				now := time.Now().Truncate(time.Hour).Unix()
				mr.Set(fmt.Sprintf("rtlm:g%d:%d", 103, now), "5")
			},
			wantPass: true,
		},
		{
			name: "已有访问记录，超过限制",
			exp: &model.Exp{
				ID:        4,
				RateLimit: createRateLimit(10, time.Hour),
			},
			group: &model.Group{
				ID: 104,
			},
			setupFunc: func() {
				now := time.Now().Truncate(time.Hour).Unix()
				mr.Set(fmt.Sprintf("rtlm:g%d:%d", 104, now), "10")
			},
			wantPass: false,
		},
		{
			name: "Redis 错误情况",
			exp: &model.Exp{
				ID:        5,
				RateLimit: createRateLimit(10, time.Hour),
			},
			group: &model.Group{
				ID: 105,
			},
			setupFunc: func() {
				mr.SetError("SIMULATED_ERROR")
			},
			wantPass: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置 Redis 状态
			mr.FlushAll()

			// 设置测试数据
			if tt.setupFunc != nil {
				tt.setupFunc()
			}

			// 执行测试
			result := service.PassLimit(ctx, tt.exp, tt.group)
			assert.Equal(t, tt.wantPass, result)

			// 验证 Redis 操作结果
			if tt.name != "实验未设置限流" && tt.name != "Redis 错误情况" && result {
				now := time.Now().Truncate(tt.exp.RateLimit.Duration).Unix()
				key := fmt.Sprintf("rtlm:g%d:%d", tt.group.ID, now)
				exists := mr.Exists(key)
				assert.True(t, exists, "限流计数器应该存在")
			}
		})
	}
}
