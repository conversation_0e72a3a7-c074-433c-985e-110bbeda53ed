package service

import (
	"context"
	"math/rand/v2"
	"strconv"
	"testing"
	"time"

	admodel "git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/data/abScheduler/repository"
	"git.7k7k.com/data/abScheduler/repository/dao"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
)

func TestRedirectService_doRedirect(t *testing.T) {
	// 设置测试环境
	db := setupTestDB()
	userStateService, mr := setupUserStateService(t)
	rdb := redis.NewClient(&redis.Options{Addr: mr.Addr()})

	// 创建 RedirectService
	rs := &RedirectService{
		DB:               (*repository.QueryAdmin)(dao.Use(db)),
		DefaultRedis:     rdb,
		UserStateService: userStateService,
	}

	t.Run("test redirect todo state", func(t *testing.T) {
		// 准备测试数据
		splitGroup := &admodel.SplitGroup{
			ID:          1,
			FromGroupID: 1,
			ToGroupID:   2,
			ToExpID:     2,
			Rate:        100,
			State:       admodel.RedirectStateTodo,
			Cnt:         1, // 添加 Cnt 字段
		}
		err := db.Create(splitGroup).Error
		require.NoError(t, err)

		uid := "utu" + strconv.Itoa(rand.IntN(10000))

		// 在 MongoDB 中插入测试用户状态
		userState := &model.UserState{
			ID:       rand.Int(),
			UID:      uid,
			GrpId:    1,
			LayerId:  1,
			ExpId:    1,
			EnterAt:  time.Now(),
			ExpireAt: time.Now().Add(24 * time.Hour),
			Rand:     1,
		}
		_, err = userStateService.mongoRepo.Coll().InsertOne(context.Background(), userState)
		require.NoError(t, err)

		// 执行重定向
		err = rs.doRedirect(context.Background(), splitGroup)
		assert.NoError(t, err)

		// 验证 Redis 中的状态
		key := rs.key(splitGroup)
		state, err := rdb.HGetAll(context.Background(), key).Result()
		assert.NoError(t, err)
		assert.NotEmpty(t, state["start_at"])
		assert.Equal(t, 0, cast.ToInt(state["total"])-cast.ToInt(state["current"]))

		// 验证数据库中的状态
		var updatedState model.UserState
		err = userStateService.mongoRepo.Coll().Find(context.Background(), bson.M{
			"uid": uid,
		}).One(&updatedState)
		assert.NoError(t, err)
		assert.Equal(t, 2, updatedState.GrpId)
	})

	t.Run("test redirect running state", func(t *testing.T) {
		// 准备测试数据
		splitGroup := &admodel.SplitGroup{
			ID:          2,
			FromGroupID: 2,
			ToGroupID:   3,
			ToExpID:     3,
			Rate:        50,
			State:       admodel.RedirectStateRunning,
		}

		// 设置 Redis 中的运行状态
		key := rs.key(splitGroup)
		err := rdb.HSet(context.Background(), key, map[string]interface{}{
			"current":  0,
			"total":    10,
			"start_at": time.Now().Unix(),
		}).Err()
		assert.NoError(t, err)

		// 在 MongoDB 中插入测试用户状态
		userState := &model.UserState{
			ID:       rand.Int(),
			UID:      "user2",
			GrpId:    2,
			LayerId:  2,
			ExpId:    2,
			EnterAt:  time.Now(),
			ExpireAt: time.Now().Add(24 * time.Hour),
		}
		_, err = userStateService.mongoRepo.Coll().InsertOne(context.Background(), userState)
		require.NoError(t, err)

		// 执行重定向
		err = rs.doRedirect(context.Background(), splitGroup)
		assert.NoError(t, err)

		// 验证数据库中的状态
		var updatedState model.UserState
		err = userStateService.mongoRepo.Coll().Find(context.Background(), bson.M{
			"uid": "user2",
		}).One(&updatedState)
		assert.NoError(t, err)
		assert.Equal(t, 3, updatedState.GrpId)
	})
}
