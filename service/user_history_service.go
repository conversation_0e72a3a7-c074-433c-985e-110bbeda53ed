package service

import (
	"context"
	"time"

	"log/slog"

	cmetric "git.7k7k.com/pkg/common/metric"
	"github.com/cockroachdb/errors"
	"github.com/qiniu/qmgo"
	"github.com/qiniu/qmgo/operator"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/data/abScheduler/repository/mongos"
)

// UserHistoryService 用户历史记录服务
// 负责管理用户的实验历史记录
type UserHistoryService struct {
	userHistoryColl func() *qmgo.Collection
	mongoMtr        *cmetric.DatabaseRequests
}

// NewUserHistoryService 创建 UserHistoryService 实例
// @autowire(set=service)
func NewUserHistoryService(mongoSet *mongos.MongoSet) *UserHistoryService {
	return &UserHistoryService{
		userHistoryColl: mongoSet.CollFn("abtest", "user_history"),
		mongoMtr:        mongoSet.Metric("abtest"),
	}
}

// GetHistory 获取用户历史记录
func (s *UserHistoryService) GetHistory(ctx context.Context, userID string) (history *model.UserHistory, err error) {
	colNameHistory := s.userHistoryColl().GetCollectionName()

	beginAt := time.Now()
	err = s.userHistoryColl().Find(ctx, bson.M{"_id": userID}).One(&history)
	timeCost := time.Since(beginAt)
	s.mongoMtr.Emit(colNameHistory, "GetHistory", "0", timeCost)
	if err != nil {
		return nil, errors.Wrapf(err, "GetHistory(%v)", userID)
	}

	return
}

// AppendHistory 追加用户的实验和方案记录
func (s *UserHistoryService) AppendHistory(ctx context.Context, states []model.UserState) error {
	colNameHistory := s.userHistoryColl().GetCollectionName()
	models := []mongo.WriteModel{}
	uids := make([]string, 0, len(states))

	for _, state := range states {
		uids = append(uids, state.UID)
		models = append(models, mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": state.UID}).
			SetUpdate(bson.M{
				operator.Push: bson.M{
					"exp_ids": state.ExpId,
					"grp_ids": state.GrpId,
				},
				operator.Set: bson.M{
					"updated_at": time.Now(),
				},
				operator.SetOnInsert: bson.M{
					"created_at": time.Now(),
				},
			}).
			SetUpsert(true))
	}

	coll, _ := s.userHistoryColl().CloneCollection()
	beginAt := time.Now()
	result, err := coll.BulkWrite(ctx, models, options.BulkWrite())
	timeCost := time.Since(beginAt)
	s.mongoMtr.Emit(colNameHistory, "BulkUpsert", "0", timeCost)

	if err != nil {
		slog.ErrorContext(ctx, "flush_to_mongo_failed", "error", err.Error(), "result", result, "uids", uids)
		return err
	}

	slog.InfoContext(ctx, "flush_history_to_db_suc", "result", result, "time_cost", timeCost.String(), "uids", uids)
	return nil
}

// DeduplicateHistory 为用户历史记录去重
// 目前在原代码中标记为 TODO
func (s *UserHistoryService) DeduplicateHistory(ctx context.Context, userID string) error {
	history, err := s.GetHistory(ctx, userID)
	if err != nil {
		if errors.Is(err, qmgo.ErrNoSuchDocuments) {
			return nil // 用户没有历史记录，无需去重
		}
		return errors.Wrap(err, "获取用户历史记录失败")
	}

	// 对实验ID和方案ID去重
	dedupExpIds := make(map[int]struct{})
	dedupGrpIds := make(map[int]struct{})

	for _, expID := range history.ExpIds {
		dedupExpIds[expID] = struct{}{}
	}

	for _, grpID := range history.GrpIds {
		dedupGrpIds[grpID] = struct{}{}
	}

	// 转换回切片
	uniqueExpIds := make([]int, 0, len(dedupExpIds))
	uniqueGrpIds := make([]int, 0, len(dedupGrpIds))

	for expID := range dedupExpIds {
		uniqueExpIds = append(uniqueExpIds, expID)
	}

	for grpID := range dedupGrpIds {
		uniqueGrpIds = append(uniqueGrpIds, grpID)
	}

	// 更新到数据库
	err = s.userHistoryColl().UpdateOne(ctx,
		bson.M{"_id": userID},
		bson.M{
			"$set": bson.M{
				"exp_ids":    uniqueExpIds,
				"grp_ids":    uniqueGrpIds,
				"updated_at": time.Now(),
			},
		},
	)

	return err
}

// BatchDeduplicateHistory 批量为用户历史记录去重
func (s *UserHistoryService) BatchDeduplicateHistory(ctx context.Context, batchSize int) error {
	cursor := primitive.NilObjectID
	processed := 0

	for {
		// 查询一批用户历史记录
		histories := make([]model.UserHistory, 0, batchSize)
		filter := bson.M{}
		if !cursor.IsZero() {
			filter["_id"] = bson.M{"$gt": cursor}
		}

		err := s.userHistoryColl().Find(ctx, filter).
			Sort("_id").
			Limit(int64(batchSize)).
			All(&histories)

		if err != nil {
			return errors.Wrap(err, "查询历史记录失败")
		}

		if len(histories) == 0 {
			break // 所有记录处理完毕
		}

		// 记录最后一条记录的ID作为下次查询的起点
		lastID := histories[len(histories)-1].Id
		objectID, _ := primitive.ObjectIDFromHex(lastID)
		cursor = objectID

		// 处理这一批数据
		bulkOps := make([]mongo.WriteModel, 0, len(histories))
		for _, history := range histories {
			// 去重逻辑
			dedupExpIds := make(map[int]struct{})
			dedupGrpIds := make(map[int]struct{})

			for _, expID := range history.ExpIds {
				dedupExpIds[expID] = struct{}{}
			}

			for _, grpID := range history.GrpIds {
				dedupGrpIds[grpID] = struct{}{}
			}

			// 转换回切片
			uniqueExpIds := make([]int, 0, len(dedupExpIds))
			uniqueGrpIds := make([]int, 0, len(dedupGrpIds))

			for expID := range dedupExpIds {
				uniqueExpIds = append(uniqueExpIds, expID)
			}

			for grpID := range dedupGrpIds {
				uniqueGrpIds = append(uniqueGrpIds, grpID)
			}

			// 添加到批量操作
			bulkOps = append(bulkOps, mongo.NewUpdateOneModel().
				SetFilter(bson.M{"_id": history.Id}).
				SetUpdate(bson.M{
					"$set": bson.M{
						"exp_ids":    uniqueExpIds,
						"grp_ids":    uniqueGrpIds,
						"updated_at": time.Now(),
					},
				}))
		}

		// 执行批量更新
		if len(bulkOps) > 0 {
			coll, _ := s.userHistoryColl().CloneCollection()
			result, err := coll.BulkWrite(ctx, bulkOps, options.BulkWrite())
			if err != nil {
				return errors.Wrap(err, "批量更新失败")
			}
			processed += int(result.ModifiedCount)
			slog.InfoContext(ctx, "批量去重历史记录",
				"已处理", processed,
				"当前批次", len(histories),
				"修改数量", result.ModifiedCount)
		}
	}

	slog.InfoContext(ctx, "批量去重历史记录完成", "总处理数量", processed)
	return nil
}

// CleanupHistory 清理超过指定时间段的历史记录
// 可选择保留最近的几条记录
func (s *UserHistoryService) CleanupHistory(ctx context.Context, userID string, retention time.Duration, keepCount int) error {
	// 获取当前历史记录
	history, err := s.GetHistory(ctx, userID)
	if err != nil {
		if errors.Is(err, qmgo.ErrNoSuchDocuments) {
			return nil // 用户没有历史记录，无需清理
		}
		return errors.Wrap(err, "获取用户历史记录失败")
	}

	// 此处仅为示例
	// 实际实现时，需要考虑历史记录的时间戳
	// 当前UserHistory模型中没有时间字段，如果需要时间清理功能，需要修改模型

	// 示例：只保留最近的keepCount条记录
	if len(history.ExpIds) > keepCount {
		startIdx := len(history.ExpIds) - keepCount
		if startIdx < 0 {
			startIdx = 0
		}
		history.ExpIds = history.ExpIds[startIdx:]
	}

	if len(history.GrpIds) > keepCount {
		startIdx := len(history.GrpIds) - keepCount
		if startIdx < 0 {
			startIdx = 0
		}
		history.GrpIds = history.GrpIds[startIdx:]
	}

	// 更新到数据库
	err = s.userHistoryColl().UpdateOne(ctx,
		bson.M{"_id": userID},
		bson.M{
			"$set": bson.M{
				"exp_ids":    history.ExpIds,
				"grp_ids":    history.GrpIds,
				"updated_at": time.Now(),
			},
		},
	)

	return err
}
