package service

import (
	"context"
	"crypto/sha1"
	"fmt"
	"sort"
	"strings"
	"time"

	"git.7k7k.com/data/abScheduler/gopkg/gmath"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/pkg/storage"
	"git.7k7k.com/pkg/storage/codec"
	sets "github.com/deckarep/golang-set/v2"
	"github.com/samber/lo"
)

type WhiteCombService struct {
	UserCache     storage.CacheKV[string, map[int]int]
	ConfigService *ConfigService
}

// @autowire(set=service)
func NewWhiteCombService(clts *redises.ClientMgr, configService *ConfigService) *WhiteCombService {
	s := &WhiteCombService{
		ConfigService: configService,
	}

	memTime := time.Minute * 15
	redisTime := time.Hour * 24 * 30
	rds := clts.BaseRedis
	s.UserCache = storage.NewProxy(
		storage.NewGoCacheAdapter[string, map[int]int](memTime, memTime),
		storage.NewRedisAdapter(rds, codec.SonicCodec[map[int]int]{}, func(id string) string { return id }, redisTime),
	).WithMetric("whitecomb")

	return s
}

//	{
//	    "project_key": "whitecomb",
//	    "layer_set": [],
//	    "expire_time": 86400
//	}
type SetWhiteCombRequest struct {
	ProjectKey   string `json:"project_key"`
	LayerSet     []int  `json:"layer_set"`
	FilterExpIds []int  `json:"filter_exp_ids"`
}

type SetWhiteCombResponse struct {
	Count       int           `json:"count"`        // 数量
	LayerGroups map[int][]int `json:"layer_groups"` // 选中的实验组
	UIDs        []string      `json:"uids"`         // 用户ID
}

// SetWhiteComb 设置白名单
//  1. 获取项目所有层
//  2. 获取所有层的所有运行中的方案
//  3. 调用 GenerateCombinationsIterative 生成所有方案的全组合
//  4. 将组合结果对应到用户ID
//  5. 写入缓存
func (s *WhiteCombService) SetWhiteComb(ctx context.Context, request *SetWhiteCombRequest) (response *SetWhiteCombResponse, err error) {
	response = &SetWhiteCombResponse{
		LayerGroups: make(map[int][]int),
	}

	// 获取项目
	prj, err := s.ConfigService.GetProject(ctx, 0, request.ProjectKey)
	if err != nil {
		return nil, err
	}

	// 获取项目所有层+方案
	layerGrps := make(map[int][]int)
	{
		layers, err := s.ConfigService.GetLayers(ctx, prj.Layers)
		if err != nil {
			return nil, err
		}
		// 过滤独占层
		if len(layers) > 0 && layers[0].Exclusive {
			layers = layers[1:]
		}
		// 过滤指定层
		if len(request.LayerSet) > 0 {
			s := sets.NewSet(request.LayerSet...)
			layers = lo.Filter(layers, func(layer *model.LayerIdx, _ int) bool {
				return s.Contains(layer.ID)
			})
		}

		filterExpIds := sets.NewSet(request.FilterExpIds...)
		for _, layer := range layers {
			_groupIds := []int{}
			_groupIds = append(_groupIds, layer.GroupIDs...)
			_groupIds = append(_groupIds, layer.PendingGroupIDs...)

			// 过滤指定的实验ID
			if !filterExpIds.IsEmpty() {
				_groupIds = lo.Filter(_groupIds, func(groupId int, _ int) bool {
					exp, _ := s.ConfigService.GroupIdx.Get(ctx, groupId)
					return filterExpIds.Contains(exp.ExpID)
				})
			}

			if len(_groupIds) == 0 {
				continue
			}

			layerGrps[layer.ID] = _groupIds
		}
	}

	layerIds := lo.Keys(layerGrps)
	sort.Ints(layerIds)
	layerGrpCnt := make([]int, len(layerIds))
	groupIds := make([][]int, len(layerIds))
	for idx, layerId := range layerIds {
		_groupIds := layerGrps[layerId]
		groupIds[idx] = _groupIds
		layerGrpCnt[idx] = len(_groupIds)
	}

	// 生成所有方案的全组合
	combs := gmath.GenerateCombinationsIterative(layerGrpCnt)

	// 从组合结果映射回 GroupID
	UIDGroups := make(map[string][]int, len(combs))
	UIDLayerGroup := make(map[string]map[int]int, len(combs))
	for _, comb := range combs {
		glist := make([]string, 0, len(comb))
		for layerIdx, grpIdx := range comb {
			glist = append(glist, fmt.Sprint(groupIds[layerIdx][grpIdx-1]))
		}
		uid := fmt.Sprint(strings.Join(glist, "-"))
		uid = fmt.Sprintf("u%x", sha1.Sum([]byte(uid)))

		UIDLayerGroup[uid] = make(map[int]int, len(comb))
		grpIds := make([]int, 0, len(comb))
		for layerIdx, grpIdx := range comb {
			grpIds = append(grpIds, groupIds[layerIdx][grpIdx-1])
			UIDLayerGroup[uid][layerIds[layerIdx]] = groupIds[layerIdx][grpIdx-1]
		}
		UIDGroups[uid] = grpIds
	}

	// 将组合结果写入缓存
	for uid, layerGroup := range UIDLayerGroup {
		err = s.UserCache.Set(ctx, s.cacheKey(request.ProjectKey, uid), layerGroup)
		if err != nil {
			return nil, err
		}
	}

	// 返回结果
	response.Count = len(combs)
	response.LayerGroups = layerGrps
	response.UIDs = make([]string, 0, len(UIDLayerGroup))
	for uid := range UIDLayerGroup {
		response.UIDs = append(response.UIDs, uid)
	}

	return
}

// GetWhiteComb 获取白名单
// TODO 合并成一次请求
func (s *WhiteCombService) Get(ctx context.Context, prjKey string, ids ...string) (map[int][]int, error) {
	r := make(map[int][]int, len(ids))
	for _, id := range ids {
		if id == "" {
			continue
		}

		cache, _ := s.UserCache.Get(ctx, s.cacheKey(prjKey, id))
		for k, v := range cache {
			r[k] = append(r[k], v)
		}
	}

	return r, nil
}

func (s *WhiteCombService) cacheKey(prjKey string, uid string) string {
	return fmt.Sprintf("whitecomb:%s:%s", prjKey, uid)
}
