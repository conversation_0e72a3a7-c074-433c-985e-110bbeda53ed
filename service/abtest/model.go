package abtest

import (
	"errors"
	"regexp"
	"strings"

	"git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/data/abScheduler/service"
)

// UID校验正则表达式：只允许字母、数字、下划线、连字符
var uidPattern = regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)

// isValidUID 检查UID是否只包含允许的字符
func isValidUID(uid string) bool {
	return uidPattern.MatchString(uid)
}

type Request struct {
	ProjectKey string         `json:"project_key"`
	Uid        string         `json:"uid"`
	DeviceID   string         `json:"device_id"`
	BundleId   string         `json:"bundle_id"`
	Attributes map[string]any `json:"attributes"`
	Debug      int32          `json:"debug"`
}

func (r *Request) ValidRequest() error {
	if r.ProjectKey == "" {
		return errors.New("project_key is required")
	}
	if r.Uid == "" {
		return errors.New("uid is required")
	}
	if len(r.U<PERSON>) < 10 {
		return errors.New("uid is too short")
	}

	// 检查UID不能包含空格
	if strings.Contains(r.Uid, " ") {
		return errors.New("uid cannot contain spaces")
	}

	// 检查UID只能包含常见字符：字母、数字、下划线、连字符
	if !isValidUID(r.Uid) {
		return errors.New("uid can only contain letters, numbers, underscores, and hyphens")
	}

	return nil
}

func (r *Request) Normalization() {
}

func (r *Request) Attr(key string) any {
	if r.Attributes == nil {
		return nil
	}

	return r.Attributes[key]
}

func (r *Request) LogAttr() map[string]any {
	attr := make(map[string]any)
	for k, v := range r.Attributes {
		if strings.Contains(k, "version") || strings.Contains(k, "country") {
			attr[k] = v
		}
	}
	return attr
}

type Response struct {
	HitSample    HitSample      `json:"hit_sample"`
	Params       map[string]any `json:"params"`
	Hits         []LayerHitInfo `json:"hits"`
	Debug        Debug          `json:"debug,omitzero"`
	CsrIndentity []string       `json:"csr_indentity,omitzero"`
}

type HitSample struct {
	ExpIds []int `json:"exps,omitzero"`
	GrpIds []int `json:"grp_ids,omitzero"`
}

type LayerHitInfo struct {
	LayerId   int    `json:"layer_id"`
	ExpId     int    `json:"exp_id"`
	GroupId   int    `json:"group_id"`
	GroupName string `json:"group_name"` // 2.0需要此字段兼容埋点
	ExpUType  int    `json:"exp_utype"`
}

type Debug = model.Debug

type LDebug = service.LDebug
