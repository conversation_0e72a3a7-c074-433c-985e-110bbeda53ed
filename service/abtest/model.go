package abtest

import (
	"errors"
	"strings"

	"git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/data/abScheduler/service"
)

type Request struct {
	ProjectKey string         `json:"project_key"`
	Uid        string         `json:"uid"`
	DeviceID   string         `json:"device_id"`
	BundleId   string         `json:"bundle_id"`
	Attributes map[string]any `json:"attributes"`
	Debug      int32          `json:"debug"`
}

func (r *Request) ValidRequest() error {
	if r.ProjectKey == "" {
		return errors.New("project_key is required")
	}
	if r.Uid == "" {
		return errors.New("uid is required")
	}
	if len(r.Uid) < 10 {
		return errors.New("uid is too short")
	}
	return nil
}

func (r *Request) Normalization() {
}

func (r *Request) Attr(key string) any {
	if r.Attributes == nil {
		return nil
	}

	return r.Attributes[key]
}

func (r *Request) LogAttr() map[string]any {
	attr := make(map[string]any)
	for k, v := range r.Attributes {
		if strings.Contains(k, "version") || strings.Contains(k, "country") {
			attr[k] = v
		}
	}
	return attr
}

type Response struct {
	HitSample    HitSample      `json:"hit_sample"`
	Params       map[string]any `json:"params"`
	Hits         []LayerHitInfo `json:"hits"`
	Debug        Debug          `json:"debug,omitzero"`
	CsrIndentity []string       `json:"csr_indentity,omitzero"`
}

type HitSample struct {
	ExpIds []int `json:"exps,omitzero"`
	GrpIds []int `json:"grp_ids,omitzero"`
}

type LayerHitInfo struct {
	LayerId   int    `json:"layer_id"`
	ExpId     int    `json:"exp_id"`
	GroupId   int    `json:"group_id"`
	GroupName string `json:"group_name"` // 2.0需要此字段兼容埋点
	ExpUType  int    `json:"exp_utype"`
}

type Debug = model.Debug

type LDebug = service.LDebug
