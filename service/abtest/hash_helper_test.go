package abtest

import (
	"fmt"
	"math"
	"strings"
	"testing"

	"git.7k7k.com/data/abScheduler/model"
	"gonum.org/v1/gonum/stat/distuv"
)

// uniformityTest 进行均匀性检验
// 参数：
//   - bucketCounts: 每个桶的实际计数
//   - totalCount: 总样本数
//
// 返回值：
//   - chiSquare: 卡方统计量，衡量观察值与期望值的偏离程度，值越大表示偏离越大
//   - pValue: 在原假设（分布均匀）下，观察到当前或更极端结果的概率
//   - p值 > 0.05：不能拒绝原假设，认为分布是均匀的
//   - p值 < 0.05：拒绝原假设，认为分布不均匀
//   - cv: 变异系数（标准差/平均值），衡量数据的离散程度
//   - cv < 0.05：表示分布非常均匀
//   - cv > 0.05：表示分布波动较大
func uniformityTest(bucketCounts []float64, totalCount int) (chiSquare, pValue, cv float64) {
	numBuckets := float64(len(bucketCounts))
	expectedCount := float64(totalCount) / numBuckets

	// 卡方检验
	chiSquare = 0.0
	for _, observed := range bucketCounts {
		diff := observed - expectedCount
		chiSquare += (diff * diff) / expectedCount
	}

	// 计算p值
	df := numBuckets - 1 // 自由度
	dist := distuv.ChiSquared{K: df}
	pValue = 1 - dist.CDF(chiSquare)

	// 计算变异系数
	mean := float64(totalCount) / numBuckets
	variance := 0.0
	for _, count := range bucketCounts {
		diff := count - mean
		variance += diff * diff
	}
	variance /= numBuckets
	stdDev := math.Sqrt(variance)
	cv = stdDev / mean

	return
}

// calculateMinSampleSize 计算给定桶数下的最小样本量要求
// numBuckets: 桶数
// confidenceLevel: 置信水平 (0.95 表示 95% 置信度)
// marginOfError: 允许的误差范围 (0.05 表示 5% 误差)
func calculateMinSampleSize(numBuckets int, confidenceLevel, marginOfError float64) (minSampleBasic, minSampleConfidence int) {
	// 1. 基于卡方检验的最小期望频数原则（每个桶至少5个样本）
	minSampleBasic = numBuckets * 5

	// 2. 基于置信区间的计算
	// 获取z统计量（95%置信度时为1.96）
	z := 1.96
	if confidenceLevel != 0.95 {
		// 这里可以添加其他置信水平的z值
		// 99% -> 2.576
		// 90% -> 1.645
		z = 1.96
	}

	// 计算基于置信区间的样本量
	minSampleConfidence = int(math.Ceil((z * z) / (4 * marginOfError * marginOfError) * float64(numBuckets)))

	return
}

// TestMinSampleSize
// Minimum Sample Size Requirements:
// Num Buckets  | Basic(n>=5)     | 95% Conf±5%     | Ratio(Conf/Buckets)
// ----------------------------------------------------------------------
// 100          | 500             | 38416           | 384.16
// 1000         | 5000            | 384160          | 384.16
// 10000        | 50000           | 3841600         | 384.16
func TestMinSampleSize(t *testing.T) {
	bucketSizes := []int{100, 1000, 10000}

	t.Logf("\nMinimum Sample Size Requirements:")
	t.Logf("%-12s | %-15s | %-15s | %s",
		"Num Buckets", "Basic(n>=5)", "95% Conf±5%", "Ratio(Conf/Buckets)")
	t.Logf("%s", strings.Repeat("-", 70))

	for _, buckets := range bucketSizes {
		basic, conf := calculateMinSampleSize(buckets, 0.95, 0.05)
		ratio := float64(conf) / float64(buckets)
		t.Logf("%-12d | %-15d | %-15d | %.2f",
			buckets, basic, conf, ratio)
	}
}

// Testing with 100 Buckets:
//
//	Sample Size     | Num Buckets  | Chi-Square         | P-Value    | CV       | Result
//	------------------------------------------------------------------------------------------
//	10000           | 100          | 93.220000          | 0.644834   | 0.096551 | FAIL
//	50000           | 100          | 85.276000          | 0.835581   | 0.041298 | PASS
//	100000          | 100          | 87.936000          | 0.779288   | 0.029654 | PASS
//	500000          | 100          | 80.435600          | 0.913714   | 0.012684 | PASS
//	1000000         | 100          | 89.869200          | 0.733101   | 0.009480 | PASS
//	5000000         | 100          | 90.124080          | 0.726720   | 0.004246 | PASS
//
// Testing with 1K Buckets:
//
//	Sample Size     | Num Buckets  | Chi-Square         | P-Value    | CV       | Result
//	------------------------------------------------------------------------------------------
//	10000           | 1000         | 1014.400000        | 0.360296   | 0.318496 | FAIL
//	50000           | 1000         | 956.840000         | 0.826861   | 0.138336 | FAIL
//	100000          | 1000         | 1011.740000        | 0.382579   | 0.100585 | FAIL
//	500000          | 1000         | 943.120000         | 0.896031   | 0.043431 | PASS
//	1000000         | 1000         | 1042.176000        | 0.166718   | 0.032283 | PASS
//	5000000         | 1000         | 960.046800         | 0.807326   | 0.013857 | PASS
//
// Testing with 10K Buckets:
//
//	Sample Size     | Num Buckets  | Chi-Square         | P-Value    | CV       | Result
//	------------------------------------------------------------------------------------------
//	10000           | 10000        | 10048.000000       | 0.362926   | 1.002397 | FAIL
//	50000           | 10000        | 10188.800000       | 0.090373   | 0.451416 | FAIL
//	100000          | 10000        | 10081.400000       | 0.279004   | 0.317512 | FAIL
//	500000          | 10000        | 9778.080000        | 0.941694   | 0.139843 | FAIL
//	1000000         | 10000        | 9871.020000        | 0.817048   | 0.099353 | FAIL
//	5000000         | 10000        | 9783.808000        | 0.936749   | 0.044235 | PASS
func TestHashUserInLayerDistribution(t *testing.T) {
	// 测试参数设置
	testConfigs := []struct {
		bucketSize int32
		name       string
	}{
		{100, "100 Buckets"},
		{1000, "1K Buckets"},
		{10000, "10K Buckets"},
	}

	for _, config := range testConfigs {
		t.Logf("\nTesting with %s:", config.name)
		t.Logf("%-15s | %-12s | %-18s | %-10s | %-8s | %s",
			"Sample Size", "Num Buckets", "Chi-Square", "P-Value", "CV", "Result")
		t.Logf("%s", strings.Repeat("-", 90))

		testCases := []struct {
			name     string
			numUsers int
		}{
			{"Small Sample", 10000},
			{"Medium Sample", 50000},
			{"Medium Sample", 100000},
			{"Large Sample", 500000},
			{"Very Large Sample", 1000000},
			{"Huge Sample", 5000000},
		}

		for _, tc := range testCases {
			// 创建测试层
			layer := &model.LayerIdx{
				ID:     123,
				Name:   "方块新用户UI层",
				Mod:    config.bucketSize,
				ModKey: []string{"uid"},
			}

			// 统计每个桶的计数
			bucketCounts := make([]float64, config.bucketSize)

			// 生成随机UID并进行分桶
			for i := 0; i < tc.numUsers; i++ {
				uid := fmt.Sprintf("user_%d", i)
				bucket := HashUserInLayer(layer, uid)
				bucketCounts[bucket]++
			}

			// 进行均匀性检验
			chiSquare, pValue, cv := uniformityTest(bucketCounts, tc.numUsers)

			// 判断测试结果
			result := "PASS"
			if pValue < 0.05 || cv > 0.05 {
				result = "FAIL"
			}

			// 输出一行测试结果
			t.Logf("%-15d | %-12d | %-18.6f | %-10.6f | %-8.6f | %s",
				tc.numUsers, config.bucketSize, chiSquare, pValue, cv, result)
		}
	}
}
