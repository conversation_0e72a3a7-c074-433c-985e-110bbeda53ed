package abtest

import (
	"context"
	"fmt"
	"log/slog"
	"slices"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/cockroachdb/errors"
	set "github.com/deckarep/golang-set/v2"
	"github.com/samber/lo"
	"github.com/samber/lo/parallel"
	"github.com/spf13/cast"

	"git.7k7k.com/data/abAdmin/idls"
	"git.7k7k.com/data/abScheduler/gopkg/algo"
	"git.7k7k.com/data/abScheduler/gopkg/csr"
	"git.7k7k.com/data/abScheduler/infra/config"
	"git.7k7k.com/data/abScheduler/infra/metric"
	"git.7k7k.com/data/abScheduler/model"
	m "git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/data/abScheduler/service"
	"git.7k7k.com/pkg/common/ctxs"
	"git.7k7k.com/pkg/common/gg"
	"git.7k7k.com/pkg/common/gosentry"
	"git.7k7k.com/pkg/common/jsonutil"
	"git.7k7k.com/pkg/common/queue"
	"git.7k7k.com/pkg/storage/codec"
)

var (
	NotFoundGroupErr = errors.New("not_found_group")
	ConditionMissErr = errors.New("condition_miss")
)

// @autowire(set=service)
type DistributeService struct {
	ConfigService    *service.ConfigService
	UserStateService *service.UserStateService
	UserLabelService *service.UserLabelService
	WhiteCombService *service.WhiteCombService
	RateService      *service.RateService
	MetricService    *service.MetricService
	HashService      *service.HashService
	abLog            *queue.KafkaQueue[model.ABLog] `wire:"-"`
	once             sync.Once                      `wire:"-"`
}

func (s *DistributeService) Init(cfg *config.Config) {
	s.once.Do(func() {
		s.abLog = queue.NewKafkaQueue[model.ABLog](queue.KafkaConfig{
			Brokers: cfg.MQ.Kafka["abtest"].Addrs,
			Topic:   "topic_ab_result",
			Async:   true,
			SSL:     cfg.MQ.Kafka["abtest"].SSL,
		})
	})
}

// Traffic 通用分流接口
func (ab *DistributeService) Traffic(ctx context.Context, request *Request) (resp *Response, err error) {
	resp = &Response{
		CsrIndentity: make([]string, 0),
		Params:       make(map[string]any),
		HitSample:    HitSample{ExpIds: make([]int, 0, 4), GrpIds: make([]int, 0, 4)},
		Hits:         make([]LayerHitInfo, 0, 4),
	}

	ctx = ctxs.Set(ctx, ctxs.KeyUserID, request.Uid)
	ctx = ctxs.Set(ctx, ctxs.KeyDeviceID, request.DeviceID)
	if request.Debug > 0 {
		ctx = context.WithValue(ctx, ctxs.KeyIsDebug, true)
	}

	// slog.InfoContext(ctx, "traffic", "request", request)

	// 1.1 基本信息
	project, err := ab.ConfigService.GetProject(ctx, 0, request.ProjectKey)
	if err != nil {
		slog.ErrorContext(ctx, "traffice:GetProject", "Key", request.ProjectKey, "Err", err)
		return nil, errors.Errorf("project [%s] not found", request.ProjectKey)
	}
	go func() {
		ab.MetricService.TouchRequest(ctx, &service.RequestLabel{
			ProjectKey: project.MetricKey(),
			UID:        request.Uid,
			Country:    strings.ToUpper(cast.ToString(request.Attr("country"))),
		})
	}()

	// 1.2 用户状态
	_, finish := gosentry.StartSpan(ctx, "ab.UserState", "", "")
	originStates, err := ab.UserStateService.Gets(ctx, project.ID, request.Uid)
	finish()
	if err != nil {
		slog.ErrorContext(ctx, "ab.UserState.Gets", "err", err)
		originStates = make(service.LayersHitState)
	}
	prjState := originStates.DeepClone()

	// 1.3 用户特征
	userLabels, err := ab.UserLabelService.BuildUserLabels(ctx, request.Uid, request.Attributes, originStates)
	if err != nil {
		return
	}
	if ctxs.IsDebug(ctx) {
		slog.InfoContext(ctx, "userLabel", "label", userLabels)
	}

	// 2.1 主分流逻辑
	resultCtxs, err := ab.handleInDomain(ctx, project, prjState, userLabels)
	if err != nil {
		slog.ErrorContext(ctx, "handleAllLayer", "err", err.Error())
		return
	}

	// TODO 这里要计算出所有新进的组；退出的组。后续重定向、更新缓存、写MQ、打点都要用

	// 2.2 运行时重定向
	extraGs, err := ab.redirect(ctx, resultCtxs, originStates, prjState)
	if err != nil {
		slog.ErrorContext(ctx, "redirect", "Err", err.Error())
		return
	}
	if len(extraGs) > 0 {
		sort.Slice(resultCtxs, func(i, j int) bool { return resultCtxs[i].Layer.Level > resultCtxs[j].Layer.Level })
	}

	// 3.1 过滤冲突的 feature
	if len(resultCtxs) > 1 {
		resultCtxs = ab.filterConflict(ctx, project, resultCtxs)
	}

	// 3.2 Merge to Response
	mergeFunc := project.GroupParamStyle.Processor().Merger()
	abParam := []byte("{}")
	debug := model.Debug{Layers: make(map[int]*model.LDebug, len(resultCtxs))}
	for _, hitCtx := range resultCtxs {
		debug.Layers[hitCtx.Layer.ID] = hitCtx.LDebug

		if !hitCtx.Hit {
			continue
		}

		// param
		group := hitCtx.Group
		_s, err := mergeFunc(abParam, gg.ResultOf(jsonutil.Default.Marshal(hitCtx.GroupParam)).Must())
		if err != nil {
			slog.ErrorContext(ctx, "MergeJson", "GroupKey", group.Key, "GroupID", group.ID, "ParamJSON", group.ParamJSON, "Err", err.Error())
			hitCtx.LDebug.ParamInvalid = true
			continue
		}
		abParam = _s

		resp.HitSample.ExpIds = append(resp.HitSample.ExpIds, group.ExpID)
		resp.HitSample.GrpIds = append(resp.HitSample.GrpIds, group.ID)
		resp.Hits = append(resp.Hits, LayerHitInfo{
			LayerId:   hitCtx.Layer.ID,
			ExpId:     group.ExpID,
			GroupId:   group.ID,
			GroupName: group.GroupName,
			ExpUType:  int(hitCtx.Exp.ActiveUType),
		})
		if hitCtx.HitState.CsrIndentity != "" {
			resp.CsrIndentity = append(resp.CsrIndentity, hitCtx.HitState.CsrIndentity)
		}
	}
	_ = codec.Default.Unmarshal(abParam, &resp.Params)
	if request.Debug > 0 {
		resp.Debug = debug
	}

	// 4. 记录新的状态
	err3 := ab.UserStateService.Update(ctx, project.ID, request.Uid, prjState, originStates)
	if err3 != nil {
		slog.ErrorContext(ctx, "UserStateUpdate err", "err", err3.Error())
	}

	// 5. 打点
	{
		prjMetricKey := project.MetricKey()
		for _, hitCtx := range resultCtxs {
			if !hitCtx.Hit {
				continue
			}

			metric.TrafficV2.WithLabelValues(prjMetricKey, strconv.Itoa(hitCtx.Layer.ID), strconv.Itoa(hitCtx.Exp.ID), hitCtx.LDebug.HitCache01()).Inc()

			metric.Traffic.WithLabelValues(prjMetricKey, strconv.Itoa(hitCtx.Layer.ID), strconv.Itoa(hitCtx.Exp.ID), strconv.Itoa(hitCtx.Group.ID)).Inc()

			ablog := model.ABLog{
				Version:    3,
				ProjectKey: project.Key,
				DistinctID: request.Uid,
				ExpID:      hitCtx.Exp.ID,
				GroupID:    hitCtx.Group.ID,
				GroupName:  hitCtx.Group.GroupName,
				LDebug:     hitCtx.LDebug,
				Attr:       request.LogAttr(),
				Ts:         time.Now().Unix(),
				BundleID:   request.BundleId,
			}
			err := ab.abLog.Push(ctx, ablog)
			if err != nil {
				slog.ErrorContext(ctx, "abLog.Push", "err", err.Error())
			}
		}
	}

	slog.InfoContext(ctx, "trafficResult", "prj", request.ProjectKey, "hit", resp.HitSample)

	return resp, nil
}

// checkGroupState 检查用户状态
func (ab *DistributeService) checkGroupState(ctx context.Context, groupState *service.HitState, now int) (*m.Group, error) {
	// dump.Dump(groupState)
	//group, err := ab.getGroup(ctx,groupState.GroupID)
	//if err != nil {
	//	return nil, err
	//}
	if groupState.ExpiredAt == 0 || (groupState.EnterAt <= now && now <= groupState.ExpiredAt) {
		group, err := ab.GetGroup(ctx, groupState.GroupID)
		if err != nil {
			slog.WarnContext(ctx, "traffice.execLayer.checkGroupState", "GroupID", groupState.GroupID, "Err", err.Error())
			return nil, err
		}
		switch idls.ExpState(group.Status) {
		case idls.ExpStatusGrayRunning, idls.ExpStatusRunning, idls.ExpStatusRunningWithoutFlow, idls.ExpStatusGrayRunningWithoutFlow:
			return group, nil
		case idls.ExpStatusClosed:
			// 处理缩圈
			if group.RedirectTo != nil {
			}
			return nil, errors.WithStack(NotFoundGroupErr)
		default:
			return nil, errors.WithStack(NotFoundGroupErr)
		}
	}
	return nil, errors.WithStack(NotFoundGroupErr)
}

var (
	ErrNoBucket = fmt.Errorf("no_bucket")
)

// execLayer 层内召回主流程：匹配一个可用实验组，cache or hash
func (ab *DistributeService) execLayer(ctx context.Context, userLabels map[string]any, hitCtx *service.HitContext) (group *m.Group, hitCache bool, csrIndentity string, err error) {
	layer := hitCtx.Layer
	state := hitCtx.HitState
	ldebug := hitCtx.LDebug
	did := ctxs.DeviceId(ctx)
	uid := ctxs.UserId(ctx)

	now := time.Now()

	// 1 检查白名单 DID
	tryGIDs := append([]int{layer.UserWhiteList[did], layer.UserWhiteList[uid]}, hitCtx.WhiteCombGrpIds...)
	if group, ok := ab.getWhite(ctx, tryGIDs); ok {
		ldebug.HitWhite = true
		slog.InfoContext(ctx, "execLayer:whiteUser", "layer", layer.Name)
		return group, false, "", nil
	}

	// TODO Deprecated 1.1 检查白名单 DID
	if did != "" {
		if group, ok := ab.checkUserWhite(ctx, layer, did); ok {
			ldebug.HitWhite = true
			slog.InfoContext(ctx, "traffice:execLayer:whiteUser", "Layer", layer.Name, "DID", did)
			return group, false, "", nil
		}
	}

	// TODO Deprecated 1.2 检查白名单 UID
	if group, ok := ab.checkUserWhite(ctx, layer, uid); ok {
		ldebug.HitWhite = true
		slog.InfoContext(ctx, "traffice:execLayer:whiteUser", "Layer", layer.Name, "User", uid)
		return group, false, "", nil
	}

	// 实验内用户特征
	uLabel := ab.UserLabelService.AppendStateLabel(userLabels, state)

	// 2.1.检查用户缓存
	if state != nil && state.GroupID != 0 {
		ldebug.HitCache = true
		if group, err = ab.checkGroupState(ctx, state, int(now.Unix())); err == nil {
			// slog.InfoContext(ctx, "HitCache", "Layer", layer.ID, "GroupID", group.ID)
			exp, err := ab.GetExp(ctx, group.ExpID)
			if err == nil {
				if exp.CanEnter(uLabel.Get, true) {
					return group, true, state.CsrIndentity, nil
				}
			}
		}
		// 这里表示用户因为触发条件不满足而退组
		ldebug.LeaveCache = state.GroupID
		state.Exit()
	}

	// 2.2 未命中缓存，重新分配实验
	// 2.2.1 检查层上是否有可用的桶
	if layer.BucketUsed == 0 {
		ldebug.MissBucket = true
		return nil, false, csrIndentity, ErrNoBucket
	}

	// 2.3. 按hash分组
	index := ab.HashService.HashUserInLayer(uid, layer)
	// index := HashUserInLayer(layer, uid)
	// metric.LayerBucketHitCount.WithLabelValues(strconv.Itoa(layer.PrjID), strconv.Itoa(layer.ID), strconv.Itoa(index%1000)).Inc()
	buckets := layer.GetBuckets()
	if index >= len(buckets) {
		slog.ErrorContext(ctx, "traffice:execLayer:hashIndex", "ModeKey", layer.ModKey, "index:", index, "bucketsLen:", len(buckets))
		return nil, false, csrIndentity, errors.WithStack(NotFoundGroupErr)
	}
	bucket := buckets[index]
	if bucket.IsEmpty() { // 命中空闲流量，本层轮空
		ldebug.MissBucket = true
		slog.InfoContext(ctx, "traffice:execLayer:bucketEmpty", "Layer", layer.Name, "BucketIndex", index, "Buckets", bucket)
		return nil, false, csrIndentity, nil
	}
	exp, err := ab.GetExp(ctx, bucket.ExpId)
	if err != nil {
		slog.ErrorContext(ctx, "execLayer.getExp", "err", err.Error())
		return nil, false, csrIndentity, err
	}

	// 3.实验进入条件判断 暂时需要修改为判断用户属性
	if !exp.CanEnter(uLabel.Get, false) {
		if ctxs.IsDebug(ctx) {
			slog.WarnContext(ctx, "TriggerUserKey Failed", "exp_id", exp.ID, "conditions", exp.Strategy.TriggerUserKey, "User", uLabel)
		}
		ldebug.TriggerFailed = true
		return nil, false, csrIndentity, nil
	}

	// 4.实验内重分配，主要针对 CSR
	groupId := bucket.GroupId
	if len(exp.CSR) > 0 {
		groupId, csrIndentity, err = ab.execCSR(ctx, layer.PrjID, layer.ID, exp.ID, exp.CSR, exp.RunningGroupIds, userLabels)
		if err != nil {
			prj, _ := ab.ConfigService.GetProject(ctx, layer.PrjID, "")
			slog.ErrorContext(ctx, "execCSR", "err", err.Error())
			metric.CSRError.WithLabelValues(prj.MetricKey(), strconv.Itoa(layer.ID), strconv.Itoa(exp.ID)).Inc()
			groupId = bucket.GroupId
			csrIndentity = ""
			err = nil
		}
	}
	group, err = ab.GetGroup(ctx, groupId)
	if err != nil {
		slog.ErrorContext(ctx, "execLayer.getGroup", "err", err.Error())
		return nil, false, csrIndentity, err
	}
	// slog.InfoContext(ctx, "execLayer.getGroup", "groupId", groupId)
	// dump.Dump(group)

	// // 5.更新用户状态
	state.Enter(group.ExpID, group.ID, exp.UserDuration)

	// prj := gg.ResultOf(ab.ConfigService.GetProject(ctx, layer.PrjID, "")).V()
	// metric.TrafficInc.WithLabelValues(prj.MetricKey(), strconv.Itoa(layer.ID), strconv.Itoa(group.ExpID), strconv.Itoa(group.ID)).Inc()

	return group, false, csrIndentity, nil
}

// checkUserWhite 检查白名单
func (ab *DistributeService) checkUserWhite(ctx context.Context, layer *m.LayerIdx, uid string) (*m.Group, bool) {
	if groupId, ok := layer.UserWhiteList[uid]; ok {
		group, err := ab.GetGroup(ctx, groupId)
		if err != nil {
			return nil, false
		}
		return group, true
	}
	return nil, false
}

// getWhite 检查白名单
func (ab *DistributeService) getWhite(ctx context.Context, gids []int) (*m.Group, bool) {
	for _, gid := range gids {
		if gid <= 0 {
			continue
		}

		group, err := ab.GetGroup(ctx, gid)
		if err == nil && group.ID > 0 {
			return group, true
		}
	}
	return nil, false
}

// GetGroup 获取指定实验组
func (ab *DistributeService) GetGroup(ctx context.Context, groupId int) (*m.Group, error) {
	// _, finish := gosentry.StartSpan(ctx, "cache.get_item", "", "Group")
	// defer finish()

	return ab.ConfigService.GroupIdx.Get(ctx, groupId)
}

// GetExp 获取指定实验
func (ab *DistributeService) GetExp(ctx context.Context, expId int) (*m.Exp, error) {
	// _, finish := gosentry.StartSpan(ctx, "cache.get_item", "", "GetExp")
	// defer finish()

	return ab.ConfigService.ExpIdx.Get(ctx, expId)
}

func (ab *DistributeService) GetLayer(ctx context.Context, layerId int) (*m.LayerIdx, error) {
	_, finish := gosentry.StartSpan(ctx, "cache.get_item", "", "GetLayer")
	defer finish()

	return ab.ConfigService.LayerIdx.Get(ctx, layerId)
}

// execRoundRobin 执行轮询算法
func (ab *DistributeService) execRoundRobin(prjId, layerId, expId int, groupIds []int) (group int, err error) {
	key := buildRoundRobinId(prjId, layerId, expId)
	schemes := buildSchemesFromGroups(groupIds)
	rrAlgo, err := algo.NewFactory().NewAlgo(algo.TypeRoundRobin, key, schemes)
	if err != nil {
		slog.Error("traffice.execLayer.execRoundRobin", "err", err)
		return 0, err
	}
	groupId := rrAlgo.Next().ID
	return groupId, nil
}

// execCSR 执行CSR
func (ab *DistributeService) execCSR(ctx context.Context, prjId, layerId, expId int, csrConfigs []*m.CSRConfig, groupIds []int, userLabels map[string]any) (group int, csrIndentity string, err error) {
	identityConfigs, keys, err := convertCSRConfigs(csrConfigs)
	if err != nil {
		return 0, "", err
	}
	csrConfig := csr.Config{
		IndentityKeys:    keys,
		IndentityConfigs: identityConfigs,
	}
	if ctxs.IsDebug(ctx) {
		slog.InfoContext(ctx, "traffice.execLayer.execCSR", "csrConfigs", csrConfigs, "csrConfig", csrConfig, "UserLabels", userLabels)
	}
	key := buildSudokuRoundRobinId(prjId, layerId, expId)
	csrIndentity, err = csr.ParseCsrIndentityFromCsrParams(csrConfig, userLabels)
	if err != nil {
		slog.Error("traffice.execLayer.execCSR", "err", err)
	} else {
		key += ":" + csrIndentity
	}

	schemes := buildSchemesFromGroups(groupIds)
	csrAlgo, err := algo.NewFactory().NewAlgo(algo.TypeSodokuRoundRobin, key, schemes)
	if err != nil {
		slog.Error("traffice.execLayer.execCSR", "err", err)
		return 0, "", err
	}
	groupId := csrAlgo.Next().ID
	slog.Info("traffice.execLayer.execCSR.Result", "Group", groupId, "csrIndentity", csrIndentity)
	return groupId, csrIndentity, err
	// return csrAlgo.Next().ID, csrIndentity, err
}

// redirect 退出实验时自动重定向
func (ab *DistributeService) redirect(ctx context.Context, hitCtxs []*service.HitContext, origin, current service.LayersHitState) (extraGroups []*m.Group, err error) {
	// slog.InfoContext(ctx, "redirect_input", "origin", origin, "current", current)

	// 找到退出的 GrpID，要对比origin和current，而不能用 hitCtxs，因为命中独占层时，压根不好轮询 origin
	exit := make([]int, 0, 2) // 记录退出的组
	for layerID, gs := range origin {
		if _, existNew := current[layerID]; !existNew || current[layerID].GroupID != gs.GroupID {
			exit = append(exit, gs.GroupID)
		}
	}
	if len(exit) == 0 { // 没有要退出的组
		return
	}

	slog.InfoContext(ctx, "redirect_exit", "exit", exit)

	// 检测重定向并执行
	for _, groupId := range exit {
		g := gg.ResultOf(ab.GetGroup(ctx, groupId)).V()
		if g == nil {
			slog.WarnContext(ctx, "redirect_get_group_nil", "groupId", groupId)
			// TODO 可能是方案关闭后，缓存没数据了
		}
		if g == nil || len(g.RedirectTo) == 0 { // 无重定向配置
			continue
		}

		sg := g.CalcRedirectForUser(ctxs.UserId(ctx))
		if sg == nil {
			continue // skip
		}
		toGId := int(sg.ToGroupID)

		tog, err := ab.GetGroup(ctx, toGId)
		if err != nil {
			return nil, errors.WithMessagef(err, "redirect_get_to_group(%v)", toGId)
		}

		slog.InfoContext(ctx, "redirect_result", "from", g.ID, "to", toGId)

		exp, _ := ab.GetExp(ctx, tog.ExpID)

		// 记录额外召回的组
		extraGroups = append(extraGroups, tog)
		// 更新 layerHitState
		current[tog.LayerID] = &service.HitState{
			ExpID:     int(tog.ExpID),
			GroupID:   int(tog.ID),
			EnterAt:   int(time.Now().Unix()),
			ExpiredAt: int(time.Now().Add(exp.UserDuration).Unix()),
		}
		// 更新 hitCtxs 原始层
		{
			// idx := slices.IndexFunc(hitCtxs, func(ctx *service.HitContext) bool { return ctx.Layer.ID == g.LayerID })
			// if idx >= 0 {
			// 	hitCtxs[idx].Hit = false
			// }
		}
		// 更新 hitCtxs 目标层
		{
			idx := slices.IndexFunc(hitCtxs, func(ctx *service.HitContext) bool { return ctx.Layer.ID == tog.LayerID })
			if idx == -1 { // 重定向到了新的层
				hitCtxs = append(hitCtxs, &service.HitContext{
					Layer:  gg.ResultOf(ab.GetLayer(ctx, tog.LayerID)).Must(),
					Hit:    true,
					LDebug: &service.LDebug{},
				})
				idx = len(hitCtxs) - 1
			}
			hitCtxs[idx].HitState = current[tog.LayerID]
			hitCtxs[idx].Group = tog
			gg.ErrOf(jsonutil.Default.UnmarshalFromString(tog.ParamJSON, &hitCtxs[idx].GroupParam)).Must()
			hitCtxs[idx].Exp = gg.ResultOf(ab.GetExp(ctx, tog.ExpID)).Must()
			hitCtxs[idx].LDebug.Redirect = true
		}
	}

	return
}

// handleInDomain 逐层处理，并处理不同优先级的合并
// 1. 确定UID所属域
// 2. 对于域内每个层，先分优先级并发召回
// 3. 按优先级排序，返回
func (ab *DistributeService) handleInDomain(ctx context.Context, project *model.ProjectIdx, userHits service.LayersHitState, userLabels map[string]any) (resultCtxs []*service.HitContext, err error) {
	domain := project.PickDomain(ctxs.UserId(ctx))
	var layerIds []int
	if domain != nil {
		layerIds = domain.LayerIDs
	} else { // 上线期间数据可能没有domain
		layerIds = project.Layers
	}
	layers, err := ab.ConfigService.GetLayers(ctx, layerIds)
	if err != nil {
		return
	}

	uid := ctxs.UserId(ctx)
	did := ctxs.DeviceId(ctx)
	whiteComb, err := ab.WhiteCombService.Get(ctx, project.Key, uid, did)
	if err != nil {
		return
	}

	for _, l := range layers {
		if userHits[l.ID] == nil {
			userHits[l.ID] = &service.HitState{}
		}
	}

	// 2. 先分优先级并发召回
	leveldLayers := lo.GroupBy(layers, func(layer *m.LayerIdx) int32 { return layer.Level })
	hitsByLevel := gg.MapMap(leveldLayers, func(level int32, layers []*m.LayerIdx) []*service.HitContext {
		// 处理各个level的层
		list := parallel.Map(layers, func(layer *m.LayerIdx, _ int) (hitCtx *service.HitContext) {
			hitCtx = &service.HitContext{
				Layer:           layer,
				HitState:        userHits[layer.ID],
				WhiteCombGrpIds: whiteComb[layer.ID],
				LDebug:          &service.LDebug{},
			}
			ab.handleLayer(ctx, userLabels, hitCtx)
			return
		})

		// 低优层：按运行时间进行排序
		if level == model.LayerLevelLow {
			sort.Slice(list, func(i, j int) bool {
				if !list[j].Hit {
					return true
				}
				if !list[i].Hit {
					return false
				}

				return list[i].Exp.StartTime.After(*list[j].Exp.StartTime)
			})
		}

		return list
	})

	lvls := []int32{model.LayerLevelZero, model.LayerLevelHig, model.LayerLevelMid, model.LayerLevelLow}
	for _, lvl := range lvls {
		hitCtxs := hitsByLevel[lvl]
		if len(hitCtxs) > 0 {
			resultCtxs = append(resultCtxs, hitCtxs...)
		}
	}

	return
}

// filterConflict 清理冲突的 feature
// 按照层级优先级(高->中->低)处理各层的实验组，确保高优先级层的特征不会被低优先级层覆盖
// 主要步骤：
// 1. 初始化特征跟踪器(已使用的特征键和特征ID)
// 2. 按优先级顺序处理各层的实验组
// 3. 对每个命中的实验组：
//   - 检查特征冲突
//   - 移除冲突特征
//   - 记录使用的非冲突特征
//
// 4. 返回处理后的实验上下文列表
// 参数：
//   - project: 项目配置索引，包含参数处理器等信息
//   - hitsByLevel: 按层级分组的实验命中上下文
//
// 返回：
//   - resultCtxs: 处理后的实验上下文列表，已移除冲突特征
func (ab *DistributeService) filterConflict(_ context.Context, project *model.ProjectIdx, hitCtxs []*service.HitContext) (resultCtxs []*service.HitContext) {
	styleProcessor := project.GroupParamStyle.Processor()

	usingFeatKeys := set.NewSet[string]()
	usingFeatIDs := []string{}
	for _, hitCtx := range hitCtxs {
		resultCtxs = append(resultCtxs, hitCtx)

		if !hitCtx.Hit {
			continue
		}

		grp := hitCtx.Group

		// 冲突
		conflictKeys, conflictIDs := hitCtx.ConflictKeys(project, grp, usingFeatKeys, usingFeatIDs)
		if len(conflictKeys) > 0 || len(conflictIDs) > 0 {
			// 只使用不冲突的 key
			hitCtx.GroupParam = styleProcessor.RemoveFeature(hitCtx.GroupParam, conflictKeys, conflictIDs)

			// LDebug
			hitCtx.LDebug.Conflict = true

			// TODO Metric
		}

		newFeatKeys := grp.FeatureKeys
		newFeatIDs := grp.FeatureIDs
		if len(conflictKeys) > 0 {
			newFeatKeys, _ = lo.Difference(newFeatKeys, conflictKeys)
		}
		if len(conflictIDs) > 0 {
			newFeatIDs, _ = lo.Difference(newFeatIDs, conflictIDs)
		}

		usingFeatKeys.Append(newFeatKeys...)
		usingFeatIDs = append(usingFeatIDs, newFeatIDs...)

		// TODO 所有需要 remove 掉的 feature 也需要加入到 using 里，这样优先级更低的层里，相关 feature 一定会被 remove
		// styleProcesser.RemoveFeature()
	}

	return
}

// handleLayer 单层处理逻辑
func (ab *DistributeService) handleLayer(ctx context.Context, userLabels map[string]any, hitCtx *service.HitContext) {
	layer := hitCtx.Layer

	// slog.InfoContext(ctx, "handleLayer.begin", "layer_id", layer.ID)

	_, finish := gosentry.StartSpan(ctx, "handleLayer", strconv.Itoa(layer.ID), layer.Name)
	defer finish()

	group, hitCache, csrIndentity, err := ab.execLayer(ctx, userLabels, hitCtx)
	if err != nil {
		if ctxs.IsDebug(ctx) || !errors.Is(err, ErrNoBucket) {
			slog.WarnContext(ctx, "execLayer.warn", "layer", layer.ID, "err", err.Error())
		}
		return
	}

	// miss
	if group == nil || group.ID <= 0 {
		return
	}

	hitCtx.Exp, _ = ab.GetExp(ctx, group.ExpID)

	// 频控。仅针对未命中缓存的情况
	if !hitCache {
		if !ab.RateService.PassLimit(ctx, hitCtx.Exp, group) {
			hitCtx.HitState.Exit()
			hitCtx.LDebug.RateLimit = true
			return
		}
	}

	hitCtx.Hit = true
	hitCtx.Group = group
	gg.ErrOf(jsonutil.Default.UnmarshalFromString(group.ParamJSON, &hitCtx.GroupParam)).Must()
	hitCtx.HitState.CsrIndentity = csrIndentity

	return
}
