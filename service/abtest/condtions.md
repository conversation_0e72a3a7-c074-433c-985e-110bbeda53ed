#### 过滤参数
* game_version 研发版本号 类型：string 版本比较
* sdk_version 中台sdk版本号 类型：string 版本比较
* gameway_num 方案号 类型: string
* is_client_panel 是否客户端兜底 类型：boolean
* old_exp 老用户桶 类型:整数 
* country 国家 类型：字符串
* is_upgrade 是否热更 类型 boolean
#### 新用户条件配置
```json
{
    "trigger_user_key": {
        "logic": "and",
        "conditions": [
            {
               "op": "=",
                "key": "gameway_num",
                "type": "string",
                "value": ""
            },
            {
                "op": ">=",
                "key": "game_version",
                "type": "string",
                "value": "3.2.6",
                "method": "client_version"
            },
            {
                "op": ">=",
                "key": "sdk_version",
                "type": "string",
                "value": "5.8.1",
                "method": "client_version"
            }
        ]
    }
}
```

#### 兜底用户配置
```json
{
    "trigger_user_key": {
        "logic": "and",
        "conditions": [
            {
               "op": "=",
                "key": "is_client_panel",
                "type": "boolean",
                "value": true
            },
            {
                "op": ">=",
                "key": "game_version",
                "type": "string",
                "value": "3.2.6",
                "method": "client_version"
            },
            {
                "op": ">=",
                "key": "sdk_version",
                "type": "string",
                "value": "5.8.1",
                "method": "client_version"
            }
        ]
    }
}
```

#### 老用户桶
```json
{
    "trigger_user_key": {
        "logic": "and",
        "conditions": [
            {
               "op": ">",
                "key": "old_exp",
                "type": "int",
                "value": 0
            },
            {
                "op": ">=",
                "key": "game_version",
                "type": "string",
                "value": "3.2.6",
                "method": "client_version"
            },
            {
                "op": ">=",
                "key": "sdk_version",
                "type": "string",
                "value": "5.8.1",
                "method": "client_version"
            }
        ]
    }
}
```

#### 用户热更升级
```json
{
    "trigger_user_key": {
        "logic": "and",
        "conditions": [
            {
               "op": "=",
                "key": "is_upgrade",
                "type": "boolean",
                "value": true
            },
            {
                "op": ">=",
                "key": "game_version",
                "type": "string",
                "value": "3.2.6",
                "method": "client_version"
            },
            {
                "op": ">=",
                "key": "sdk_version",
                "type": "string",
                "value": "5.8.1",
                "method": "client_version"
            }
        ]
    }
}
```

#### 国家过滤
```json
{
    "trigger_user_key": {
        "logic": "and",
        "conditions": [
            {
               "op": "in",
                "key": "country",
                "type": "string",
                "value": ["cn", "us"]
            },
            {
                "op": ">=",
                "key": "game_version",
                "type": "string",
                "value": "3.2.6",
                "method": "client_version"
            },
            {
                "op": ">=",
                "key": "sdk_version",
                "type": "string",
                "value": "5.8.1",
                "method": "client_version"
            }
        ]
    }
}
```