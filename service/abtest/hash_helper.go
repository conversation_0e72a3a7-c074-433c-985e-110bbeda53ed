package abtest

import (
	"bytes"
	"fmt"
	"slices"
	"strconv"
	"strings"

	"github.com/cockroachdb/errors"

	"git.7k7k.com/data/abAdmin/gopkg/condition"
	"git.7k7k.com/data/abScheduler/gopkg/algo"
	"git.7k7k.com/data/abScheduler/gopkg/csr"
	"git.7k7k.com/data/abScheduler/model"
	"github.com/bytedance/sonic"
	"github.com/spaolacci/murmur3"
)

// parseGroupParamJSON 转换group.ParamJSON
func parseGroupParamJSON(data string) (result map[string]any, err error) {
	err = sonic.Unmarshal([]byte(data), &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

var (
	hashFunc = murmur3.Sum64
	// hashFunc2 = func(data []byte) uint64 { // 均匀性非常差
	// 	h := fnv.New64a()
	// 	h.Write(data)
	// 	return h.Sum64()
	// }
	// hashFunc3 = xxhash.Sum64 // 均匀性跟 murmur3.Sum64 区别不大
)

// key := hashString(getModString(strconv.Itoa(layer.ID)+"_"+layer.Name, layer.ModKey, req))
func HashUserInLayer(layer *model.LayerIdx, uid string) (bucketIdx int) {
	key := hashFunc(getModString(strconv.Itoa(layer.ID), layer.ModKey, uid))
	return int(key % uint64(layer.Mod))
}

func getModString(prefix string, modKeys []string, uid string) []byte {
	strBuilder := bytes.Buffer{}
	strBuilder.WriteString(prefix)
	strBuilder.WriteString(":")
	for _, key := range modKeys {
		switch key {
		case "uid":
			strBuilder.WriteString(uid)
		}
	}
	return strBuilder.Bytes()
}

func buildRoundRobinId(prjId, layerId, expId int) string {
	return fmt.Sprintf(RoundRobinKey, prjId, layerId, expId)
}

func buildSudokuRoundRobinId(prjId, layerId, expId int) string {
	return fmt.Sprintf(SudokuRoundRobinKey, prjId, layerId, expId)
}

// convertCSRConfigs
func convertCSRConfigs(csrConfigs []*model.CSRConfig) (csr.IndentityConfigs, []string, error) {
	slices.SortFunc(csrConfigs, func(a, b *model.CSRConfig) int {
		return strings.Compare(a.Key, b.Key)
	})
	indentityConfigs := make(csr.IndentityConfigs)
	keys := make([]string, 0, len(csrConfigs))
	for _, csrConfig := range csrConfigs {
		var kind string
		switch csrConfig.Type {
		case condition.ListString, condition.ListStringDefault, condition.ListInt, condition.ListIntDefault:
			kind = csr.KindList
		case condition.RangeFloat, condition.RangeInt, condition.RangeIntDefault:
			kind = csr.KindRange
		// case CSRTypeGroup:
		// 	kind = csr.KindGroup
		default:
			continue
		}
		v, ok := csrConfig.Value.([]any)
		if !ok {
			continue
		}
		indentityConfig := csr.IndentityConfig{
			Kind:  kind,
			Value: v,
		}
		indentityConfigs[csrConfig.Key] = indentityConfig
		keys = append(keys, csrConfig.Key)
	}
	slices.Sort(keys)
	if len(keys) == 0 {
		return nil, nil, errors.New("no valid CSR identity")
	}
	return indentityConfigs, keys, nil
}

func buildSchemesFromGroups(groups []int) []algo.Scheme {
	schemes := make([]algo.Scheme, len(groups))
	for i, group := range groups {
		schemes[i] = algo.Scheme{ID: group}
	}
	return schemes
}
