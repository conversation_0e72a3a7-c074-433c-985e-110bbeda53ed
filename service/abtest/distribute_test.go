package abtest

import (
	"context"
	"testing"
	"time"

	"git.7k7k.com/data/abAdmin/gopkg/condition"
	"git.7k7k.com/data/abScheduler/model"
	m "git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/data/abScheduler/service"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func TestService_redirect(t *testing.T) {
	tests := []struct {
		name        string
		origin      service.LayersHitState
		current     service.LayersHitState
		wantErr     bool
		wantCurrent service.LayersHitState
		applyPatch  func(*DistributeService, *gomonkey.Patches)
	}{
		{
			name: "正常重定向",
			origin: service.LayersHitState{
				1: &service.HitState{GroupID: 100},
			},
			current: service.LayersHitState{},
			wantErr: false,
			wantCurrent: service.LayersHitState{
				2: &service.HitState{
					GroupID:   200,
					EnterAt:   int(time.Now().Unix()),
					ExpiredAt: int(time.Now().Add(time.Hour * 24).Unix()),
				},
			},
			applyPatch: func(s *DistributeService, patches *gomonkey.Patches) {
				patches.ApplyMethodFunc(s, "GetGroup", func(_ context.Context, groupID int) (*model.Group, error) {
					if groupID == 100 {
						return &model.Group{RedirectTo: []*model.SplitGroup{{ToGroupID: 200}}, ParamJSON: "{}"}, nil
					}
					if groupID == 200 {
						return &model.Group{ID: 200, LayerID: 2, RedirectTo: nil, ParamJSON: "{}"}, nil
					}
					return nil, nil
				})
			},
		},
		{
			name: "无需重定向",
			origin: service.LayersHitState{
				1: &service.HitState{GroupID: 100},
			},
			current: service.LayersHitState{
				1: &service.HitState{GroupID: 100},
			},
			wantErr: false,
			wantCurrent: service.LayersHitState{
				1: &service.HitState{GroupID: 100},
			},
			applyPatch: func(s *DistributeService, patches *gomonkey.Patches) {
				patches.ApplyMethodFunc(s, "GetGroup", func(_ context.Context, groupID int) (*model.Group, error) {
					if groupID == 100 {
						return &model.Group{RedirectTo: []*model.SplitGroup{{ToGroupID: 200}}, ParamJSON: "{}"}, nil
					}
					return nil, nil
				})
			},
		},
		{
			name: "无重定向配置",
			origin: service.LayersHitState{
				1: &service.HitState{GroupID: 100},
			},
			current:     service.LayersHitState{},
			wantErr:     false,
			wantCurrent: service.LayersHitState{},
			applyPatch: func(s *DistributeService, patches *gomonkey.Patches) {
				patches.ApplyMethodFunc(s, "GetGroup", func(_ context.Context, groupID int) (*model.Group, error) {
					if groupID == 100 {
						return &model.Group{ParamJSON: "{}"}, nil
					}
					return nil, nil
				})
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &DistributeService{}

			// Mock GetGroup和getExp方法
			patches := gomonkey.NewPatches()
			defer patches.Reset()
			patches.ApplyMethodFunc(s, "GetExp", func(_ context.Context, expId int) (*m.Exp, error) {
				return &model.Exp{
					UserDuration: time.Hour * 24,
				}, nil
			})
			patches.ApplyMethodFunc(s, "GetLayer", func(_ context.Context, layerId int) (*m.LayerIdx, error) {
				return &model.LayerIdx{
					ID: layerId,
				}, nil
			})
			if tt.applyPatch != nil {
				tt.applyPatch(s, patches)
			}

			hitCtxs := make([]*service.HitContext, 0, len(tt.origin))
			_, err := s.redirect(context.Background(), hitCtxs, tt.origin, tt.current)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// 验证当前状态
			if len(tt.wantCurrent) > 0 {
				for layerID, wantState := range tt.wantCurrent {
					gotState, exists := tt.current[layerID]
					assert.True(t, exists)
					assert.Equal(t, wantState.GroupID, gotState.GroupID)
					// 由于时间戳的原因,这里只验证时间差值在1秒以内
					assert.InDelta(t, wantState.EnterAt, gotState.EnterAt, 1)
					assert.InDelta(t, wantState.ExpiredAt, gotState.ExpiredAt, 1)
				}
			}
		})
	}
}

func TestService_execCSR(t *testing.T) {
	tests := []struct {
		name       string
		prjId      int
		layerId    int
		expId      int
		csrConfigs []*m.CSRConfig
		groupIds   []int
		userLabels map[string]any
		wantGroup  int
		wantCSRId  string
		wantErr    bool
		applyPatch func(*DistributeService, *gomonkey.Patches)
	}{
		{
			name:    "正常CSR分流",
			prjId:   1,
			layerId: 1,
			expId:   1,
			csrConfigs: []*m.CSRConfig{
				{
					Key:   "country",
					Type:  condition.ListString,
					Value: []any{"cn", "us"},
				},
				{
					Key:   "ecpm",
					Type:  condition.RangeFloat,
					Value: []any{2, 4, 6},
				},
			},
			groupIds: []int{100, 200},
			userLabels: map[string]any{
				"country": "cn",
				"ecpm":    5,
			},
			wantGroup: 100,
			wantCSRId: "1_1",
			wantErr:   false,
			applyPatch: func(s *DistributeService, patches *gomonkey.Patches) {
			},
		},
		{
			name:    "CSR配置错误",
			prjId:   1,
			layerId: 1,
			expId:   1,
			csrConfigs: []*m.CSRConfig{
				{
					Key:   "",
					Value: "",
				},
			},
			groupIds:   []int{100, 200},
			userLabels: map[string]any{},
			wantGroup:  0,
			wantCSRId:  "",
			wantErr:    true,
		},
		{
			name:    "用户标签不匹配",
			prjId:   1,
			layerId: 1,
			expId:   1,
			csrConfigs: []*m.CSRConfig{
				{
					Key:   "uid",
					Value: "123",
				},
			},
			groupIds: []int{100, 200},
			userLabels: map[string]any{
				"uid": "456",
			},
			wantGroup: 0,
			wantCSRId: "",
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &DistributeService{}
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			if tt.applyPatch != nil {
				tt.applyPatch(s, patches)
			}

			gotGroup, gotCSRId, err := s.execCSR(context.Background(), tt.prjId, tt.layerId, tt.expId, tt.csrConfigs, tt.groupIds, tt.userLabels)

			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.wantGroup, gotGroup)
			assert.Equal(t, tt.wantCSRId, gotCSRId)
		})
		break
	}
}

func TestRemoveConflict(t *testing.T) {
	type testCase struct {
		name     string
		project  *model.ProjectIdx
		input    []*service.HitContext
		wantHits []*service.HitContext
		assert   func(t *testing.T, hits []*service.HitContext)
	}

	tests := []testCase{
		{
			name: "正常处理-无冲突-JSON格式",
			project: &model.ProjectIdx{
				GroupParamStyle: "",
			},
			input: []*service.HitContext{
				{
					Layer: &model.LayerIdx{Level: 2},
					Group: &model.Group{
						ID:          1,
						GroupName:   "test1",
						FeatureKeys: []string{"feat1", "feat2"},
						FeatureIDs:  []string{"1001", "1002"},
						ParamJSON:   `{"feat1":"val1","feat2":"val2"}`,
					},
					Hit:        true,
					LDebug:     &service.LDebug{},
					GroupParam: map[string]any{"feat1": "val1", "feat2": "val2"},
				},
				{
					Layer: &model.LayerIdx{Level: 1},
					Group: &model.Group{
						ID:          2,
						GroupName:   "test2",
						FeatureKeys: []string{"feat3", "feat4"},
						FeatureIDs:  []string{"2001", "2002"},
						ParamJSON:   `{"feat3":"val3","feat4":"val4"}`,
					},
					Hit:        true,
					LDebug:     &service.LDebug{},
					GroupParam: map[string]any{"feat3": "val3", "feat4": "val4"},
				},
			},
			assert: func(t *testing.T, hits []*service.HitContext) {
				assert.Len(t, hits, 2)
				assert.True(t, hits[0].Hit)
				assert.True(t, hits[1].Hit)
				assert.Equal(t, map[string]any{"feat1": "val1", "feat2": "val2"}, hits[0].GroupParam)
				assert.Equal(t, map[string]any{"feat3": "val3", "feat4": "val4"}, hits[1].GroupParam)
				assert.False(t, hits[0].LDebug.Conflict)
				assert.False(t, hits[1].LDebug.Conflict)
			},
		},
		{
			name: "正常处理-无冲突-Block格式",
			project: &model.ProjectIdx{
				GroupParamStyle: "block",
			},
			input: []*service.HitContext{
				{
					Layer: &model.LayerIdx{Level: 2},
					Group: &model.Group{
						ID:          1,
						GroupName:   "test1",
						FeatureKeys: []string{"feat1", "feat2"},
						FeatureIDs:  []string{"1001", "1002"},
						ParamJSON:   `{"features":[{"id":1001,"value":"val1"},{"id":1002,"value":"val2"}]}`,
					},
					Hit:    true,
					LDebug: &service.LDebug{},
					GroupParam: map[string]any{
						"features": []any{
							map[string]any{"id": 1001, "value": "val1"},
							map[string]any{"id": 1002, "value": "val2"},
						},
					},
				},
				{
					Layer: &model.LayerIdx{Level: 1},
					Group: &model.Group{
						ID:          2,
						GroupName:   "test2",
						FeatureKeys: []string{"feat3", "feat4"},
						FeatureIDs:  []string{"2001", "2002"},
						ParamJSON:   `{"features":[{"id":2001,"value":"val3"},{"id":2002,"value":"val4"}]}`,
					},
					Hit:    true,
					LDebug: &service.LDebug{},
					GroupParam: map[string]any{
						"features": []any{
							map[string]any{"id": 2001, "value": "val3"},
							map[string]any{"id": 2002, "value": "val4"},
						},
					},
				},
			},
			assert: func(t *testing.T, hits []*service.HitContext) {
				assert.Len(t, hits, 2)
				assert.True(t, hits[0].Hit)
				assert.True(t, hits[1].Hit)
				assert.Equal(t, map[string]any{"features": []any{
					map[string]any{"id": 1001, "value": "val1"},
					map[string]any{"id": 1002, "value": "val2"},
				}}, hits[0].GroupParam)
				assert.Equal(t, map[string]any{"features": []any{
					map[string]any{"id": 2001, "value": "val3"},
					map[string]any{"id": 2002, "value": "val4"},
				}}, hits[1].GroupParam)
				assert.False(t, hits[0].LDebug.Conflict)
				assert.False(t, hits[1].LDebug.Conflict)
			},
		},
		{
			name: "处理特征冲突-JSON格式",
			project: &model.ProjectIdx{
				GroupParamStyle: "",
			},
			input: []*service.HitContext{
				{
					Layer: &model.LayerIdx{Level: 2},
					Group: &model.Group{
						ID:          1,
						GroupName:   "test1",
						FeatureKeys: []string{"feat1", "feat2"},
						FeatureIDs:  []string{"1001", "1002"},
						ParamJSON:   `{"feat1":"val1","feat2":"val2"}`,
					},
					Hit:        true,
					LDebug:     &service.LDebug{},
					GroupParam: map[string]any{"feat1": "val1", "feat2": "val2"},
				},
				{
					Layer: &model.LayerIdx{Level: 1},
					Group: &model.Group{
						ID:          2,
						GroupName:   "test2",
						FeatureKeys: []string{"feat1", "feat3"},
						FeatureIDs:  []string{"1001", "2001"},
						ParamJSON:   `{"feat1":"val3","feat3":"val4"}`,
					},
					Hit:        true,
					LDebug:     &service.LDebug{},
					GroupParam: map[string]any{"feat1": "val3", "feat3": "val4"},
				},
			},
			assert: func(t *testing.T, hits []*service.HitContext) {
				assert.Len(t, hits, 2)
				assert.True(t, hits[0].Hit)
				assert.True(t, hits[1].Hit)
				assert.Equal(t, map[string]any{"feat1": "val1", "feat2": "val2"}, hits[0].GroupParam)
				assert.Equal(t, map[string]any{"feat3": "val4"}, hits[1].GroupParam)
				assert.False(t, hits[0].LDebug.Conflict)
				assert.True(t, hits[1].LDebug.Conflict)
			},
		},
		{
			name: "处理特征冲突-Block格式",
			project: &model.ProjectIdx{
				GroupParamStyle: "block",
			},
			input: []*service.HitContext{
				{
					Layer: &model.LayerIdx{Level: 2},
					Group: &model.Group{
						ID:          1,
						GroupName:   "test1",
						FeatureKeys: []string{"1001", "1002"},
						FeatureIDs:  []string{"1001001", "1002001"},
						ParamJSON:   `{"features":[{"id":1001001,"value":"val1"},{"id":1002001,"value":"val2"}]}`,
					},
					Hit:    true,
					LDebug: &service.LDebug{},
					GroupParam: map[string]any{
						"features": []any{
							map[string]any{"id": 1001001, "value": "val1"},
							map[string]any{"id": 1002001, "value": "val2"},
						},
					},
				},
				{
					Layer: &model.LayerIdx{Level: 1},
					Group: &model.Group{
						ID:          2,
						GroupName:   "test2",
						FeatureKeys: []string{"1001", "2001"},
						FeatureIDs:  []string{"1001001", "2001001"},
						ParamJSON:   `{"features":[{"id":1001001,"value":"val3"},{"id":2001001,"value":"val4"}]}`,
					},
					Hit:    true,
					LDebug: &service.LDebug{},
					GroupParam: map[string]any{
						"features": []any{
							map[string]any{"id": 1001001, "value": "val3"},
							map[string]any{"id": 2001001, "value": "val4"},
						},
					},
				},
			},
			assert: func(t *testing.T, hits []*service.HitContext) {
				assert.Len(t, hits, 2)
				assert.True(t, hits[0].Hit)
				assert.True(t, hits[1].Hit)
				assert.Equal(t, map[string]any{"features": []any{
					map[string]any{"id": 1001001, "value": "val1"},
					map[string]any{"id": 1002001, "value": "val2"},
				}}, hits[0].GroupParam)
				assert.Equal(t, map[string]any{"features": []any{
					map[string]any{"id": 2001001, "value": "val4"},
				}}, hits[1].GroupParam)
				assert.False(t, hits[0].LDebug.Conflict)
				assert.True(t, hits[1].LDebug.Conflict)
			},
		},
		{
			name: "处理特征冲突-Block格式-包含一层excludeIDs",
			project: &model.ProjectIdx{
				GroupParamStyle: "block",
			},
			input: []*service.HitContext{
				{
					Layer: &model.LayerIdx{Level: 2},
					Group: &model.Group{
						ID:          1,
						GroupName:   "test1",
						FeatureKeys: []string{"1001", "1002"},
						FeatureIDs:  []string{"1001001", "1002001", "2001001"},
						ParamJSON:   `{"features":[{"id":1001001,"value":"val1"},{"id":1002001,"value":"val2"}],"exclude_ids": [2001001]}`,
					},
					Hit:    true,
					LDebug: &service.LDebug{},
					GroupParam: map[string]any{
						"features": []any{
							map[string]any{"id": 1001001, "value": "val1"},
							map[string]any{"id": 1002001, "value": "val2"},
						},
						"exclude_ids": []any{2001001},
					},
				},
				{
					Layer: &model.LayerIdx{Level: 1},
					Group: &model.Group{
						ID:          2,
						GroupName:   "test2",
						FeatureKeys: []string{"1001", "2001"},
						FeatureIDs:  []string{"1001001", "2001001"},
						ParamJSON:   `{"features":[{"id":1001001,"value":"val3"},{"id":2001001,"value":"val4"}]}`,
					},
					Hit:    true,
					LDebug: &service.LDebug{},
					GroupParam: map[string]any{
						"features": []any{
							map[string]any{"id": 1001001, "value": "val3"},
							map[string]any{"id": 2001001, "value": "val4"},
						},
					},
				},
			},
			assert: func(t *testing.T, hits []*service.HitContext) {
				assert.Len(t, hits, 2)
				assert.True(t, hits[0].Hit)
				assert.True(t, hits[1].Hit)
				assert.Equal(t, []any{
					map[string]any{"id": 1001001, "value": "val1"},
					map[string]any{"id": 1002001, "value": "val2"},
				}, hits[0].GroupParam["features"])
				assert.Equal(t, map[string]any{"features": []any{}}, hits[1].GroupParam)
				assert.False(t, hits[0].LDebug.Conflict)
				assert.True(t, hits[1].LDebug.Conflict)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &DistributeService{
				ConfigService: &service.ConfigService{},
			}

			// Mock GetProject method
			patches := gomonkey.NewPatches()
			defer patches.Reset()
			patches.ApplyMethodFunc(s.ConfigService, "GetProject", func(ctx context.Context, prjId int, key string) (*model.ProjectIdx, error) {
				return tt.project, nil
			})

			hits := s.filterConflict(context.Background(), tt.project, tt.input)
			tt.assert(t, hits)
		})
	}
}
