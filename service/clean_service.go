package service

import (
	"context"
	"log/slog"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abScheduler/infra/metric"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/data/abScheduler/repository"
	"github.com/cockroachdb/errors"
	"github.com/panjf2000/ants/v2"
	"github.com/redis/go-redis/v9"
)

// @autowire(set=service)
type CleanService struct {
	DB               *repository.QueryAdmin
	ConfigService    *ConfigService
	UserStateService *UserStateService
	RedisClients     *redises.ClientMgr
}

// FindClosedAndRemoveCache 寻找并清理最近关闭的实验缓存
func (s *CleanService) FindClosedAndRemoveCache(ctx context.Context, after time.Time) (err error) {
	dao := s.DB.Exp.UnderlyingDB()
	cursor := int64(0)
	pageSize := 100
	for {
		items := make([]model.Exp, 0, pageSize)
		query := dao.WithContext(ctx).Where("state = 5").Limit(pageSize).Order("id DESC")
		if cursor > 0 {
			query = query.Where("id < ?", cursor)
		}
		if !after.IsZero() {
			query = query.Where("update_time >= ?", after)
		}
		err = query.Find(&items).Error
		if err != nil {
			return err
		}
		if len(items) == 0 {
			break
		}
		cursor = items[len(items)-1].ID

		for _, item := range items {
			err := s.RemoveExpUserCache(ctx, int(item.ProjectID), int(item.LayerID), int(item.ID))
			if err != nil {
				slog.ErrorContext(ctx, "RemoveExpUserCache", "err", err)
				return err
			}
		}
	}

	return
}

// RemoveExpUserCache 清理实验缓存
func (s *CleanService) RemoveExpUserCache(ctx context.Context, prjID, layerID, expID int) (err error) {
	if layerID == 0 {
		exp, err := s.ConfigService.ExpIdx.Get(ctx, expID)
		if err != nil {
			return err
		}
		layerID = exp.LayerID
	}

	slog.InfoContext(ctx, "RemoveExpUserCache start", "prjID", prjID, "layerID", layerID, "expID", expID)

	uidsCh, err := s.UserStateService.ListWithExp(ctx, prjID, expID)
	if err != nil {
		return err
	}

	gp, err := ants.NewPool(1)
	if err != nil {
		return err
	}
	defer gp.Release()

	wg := sync.WaitGroup{}

	uidScaned := int64(0)
	uidRemoved := int64(0)
	for uids := range uidsCh {
		wg.Add(1)
		gp.Submit(func() {
			defer wg.Done()

			atomic.AddInt64(&uidScaned, int64(len(uids)))
			for _, uid := range uids {
				hitState, err := s.UserStateService.GetOneCache(ctx, prjID, uid, layerID)
				if err != nil {
					if errors.Is(err, redis.Nil) {
						continue
					}
					slog.ErrorContext(ctx, "RemoveExpUserCache", "uid", uid, "err", err)
					continue
				}

				if hitState.ExpID != expID { // 如果已经记录了新实验，不清理
					continue
				}

				err = s.UserStateService.RemoveCache(ctx, prjID, uid, layerID)
				if err != nil {
					slog.ErrorContext(ctx, "RemoveExpUserCache", "uid", uid, "err", err)
					continue
				}

				metric.CacheRemoveCount.WithLabelValues(strconv.Itoa(prjID), strconv.Itoa(layerID), strconv.Itoa(expID)).Inc()

				atomic.AddInt64(&uidRemoved, 1)
			}
			slog.InfoContext(ctx, "RemoveExpUserCache", "uidScaned", uidScaned, "uidRemoved", uidRemoved)
		})
	}
	wg.Wait()

	slog.InfoContext(ctx, "RemoveExpUserCache done", "expID", expID, "uidScaned", uidScaned, "uidRemoved", uidRemoved)
	return
}
