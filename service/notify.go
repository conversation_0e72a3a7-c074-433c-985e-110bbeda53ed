package service

import (
	"context"

	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/pkg/common/notifier"
)

type NotifyService struct {
	ExpNotifier       *notifier.Notifier[NotifyMsg]
	UserStateNotifier *notifier.Notifier[UserStateNotifyMsg]
}

// NewNotifyService 创建一个通知服务
// @autowire(set=serevice)
func NewNotifyService(rds *redises.ClientMgr) *NotifyService {
	return &NotifyService{
		ExpNotifier: notifier.New(context.Background(), &notifier.NotifyConfig[NotifyMsg]{
			RedisClient:  rds.<PERSON>Red<PERSON>,
			RedisKey:     "exp_update",
			ConsumerSelf: true,
		}),
		UserStateNotifier: notifier.New(context.Background(), &notifier.NotifyConfig[UserStateNotifyMsg]{
			RedisClient:  rds.ChanRedis,
			RedisKey:     "user_state_update",
			ConsumerSelf: true,
		}),
	}
}
