package service

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/data/abScheduler/model"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
)

type RateService struct {
	redisCli redis.UniversalClient
}

// NewRateService 创建一个限流服务
// @autowire(set=serevice)
func NewRateService(redisClts *redises.ClientMgr) *RateService {
	return &RateService{redisCli: redisClts.DefaultRedis}
}

// PassLimit 方案限流
// 1. 只有实验需要限流时才生效
// 2. 每次直接 incr，修改限流后需要下个小时生效
func (s *RateService) PassLimit(ctx context.Context, exp *model.Exp, g *model.Group) bool {
	// 如果实验没有设置限流，则直接通过
	r := exp.RateLimit
	if r == nil {
		return true
	}

	key := fmt.Sprintf("g%d:%d", g.ID, time.Now().Truncate(r.Duration).Unix())

	ok := s.incrBelowLimit(ctx, key, int(r.Limit), r.Duration*3)
	// slog.InfoContext(ctx, "rate_limit", "key", key, "limit", r.Limit, "duration", r.Duration.String(), "ok", ok)
	return ok
}

// incrBelowLimit 判断是否命中限流
// 1. 如果 incr 小于等于 1，则设置过期时间
// 2. 如果 incr 大于 limit，则返回 false
// 无论是否命中，都会 incr
func (s *RateService) incrBelowLimit(ctx context.Context, key string, limit int, timeout time.Duration) bool {
	key = fmt.Sprintf("rtlm:%s", key)
	incr, err := s.redisCli.Incr(ctx, key).Result()
	if err != nil {
		return false
	}
	if incr > int64(limit) {
		return false
	}

	if incr <= 1 {
		_, err := lo.Attempt(3, func(index int) error {
			return s.redisCli.Expire(ctx, key, timeout).Err()
		})
		if err != nil {
			slog.ErrorContext(ctx, "rate_expire", "err", err.Error())
		}
	}

	return true
}
