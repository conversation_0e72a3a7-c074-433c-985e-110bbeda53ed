package service

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	adminmodel "git.7k7k.com/data/abAdmin/model"
	admodel "git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/data/abScheduler/repository"
	"git.7k7k.com/pkg/common/async"
	"github.com/go-redsync/redsync/v4"
	"github.com/panjf2000/ants/v2"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
)

// RedirectService
// 监听重定向需求，更新用户状态
// @autowire(set=service)
type RedirectService struct {
	DB               *repository.QueryAdmin // 从 DB 读原始数据
	Redsync          *redsync.Redsync       // 分布式锁
	DefaultRedis     redis.UniversalClient  // 迁移状态
	UserStateService *UserStateService      // 更新用户状态

	AutoRedirectService

	consumer *ants.Pool `wire:"-"`
	initOnce sync.Once  `wire:"-"`
}

func (rs *RedirectService) init() {
	rs.initOnce.Do(func() {
		var err error
		rs.consumer, err = ants.NewPool(2)
		if err != nil {
			panic(err)
		}
	})
}

func (rs *RedirectService) RunOnce(ctx context.Context) (err error) {
	rlock := rs.Redsync
	changes, err := rs.getChanged(ctx)
	if err != nil {
		return
	}

	for _, change := range changes {
		mutex := rlock.NewMutex("redirect_"+cast.ToString(change.FromGroupID), redsync.WithExpiry(time.Minute*15))

		err := mutex.TryLockContext(ctx)
		if err != nil {
			slog.ErrorContext(ctx, "redirect lock", "error", err.Error())
			continue
		}

		err = rs.doRedirect(ctx, change)
		slog.InfoContext(ctx, "do redirect", "change", change.ID, "err", err)
		if err != nil {
			slog.ErrorContext(ctx, fmt.Sprintf("%+v", err))
		}

		ok, err := mutex.Unlock()
		if !ok {
			slog.ErrorContext(ctx, "redirect unlock", "error", err.Error())
		}
	}

	return nil
}

func (rs *RedirectService) getChanged(ctx context.Context) (changes []*admodel.SplitGroup, err error) {
	spg := rs.DB.SplitGroup
	changes, err = spg.WithContext(ctx).
		Where(spg.State.In(admodel.RedirectStateTodo, admodel.RedirectStateRunning)).
		Where(spg.SplitType.In(admodel.SplitGroupTypeClose, admodel.SplitGroupTypeRedirect)).
		Find()
	if err != nil {
		return
	}

	return
}

// FireRedirect 手动触发重定向
func (rs *RedirectService) FireRedirect(ctx context.Context, changeId int) (err error) {
	change, err := rs.DB.SplitGroup.Where(rs.DB.SplitGroup.ID.Eq(int64(changeId))).First()
	if err != nil {
		return
	}

	return rs.doRedirect(ctx, change)
}

// doRedirect 执行重定向
// 1. 轮询所有匹配的 UserState
// 2. updateUserStateCluster
// 2.1. 逐条更新 DB
// 2.2. 重建为 uid 维度
// 2.3. 更新到 redis
// 2.4. 通知集群其他节点更新本地缓存
func (rs *RedirectService) doRedirect(ctx context.Context, change *admodel.SplitGroup) (err error) {
	rs.init()

	rds := rs.DefaultRedis
	rdsKey := rs.key(change)
	dao := rs.DB.SplitGroup.UnderlyingDB()

	toGrp, err := rs.DB.Group.Where(rs.DB.Group.ID.Eq(int64(change.ToGroupID))).First()
	if err != nil {
		return
	}
	toExp, err := rs.DB.Exp.Where(rs.DB.Exp.ID.Eq(int64(toGrp.ExpID))).First()
	if err != nil {
		return
	}

	mgo := rs.UserStateService.mongoRepo.Coll()

	// 首次重定向，需要初始化任务信息
	if change.State == admodel.RedirectStateTodo || change.State == 11 {
		need := change.Cnt // 需要重定向的人数
		state := map[string]any{
			"current":  0,
			"total":    need,
			"start_at": time.Now().Unix(),
		}
		_, err = rds.HSet(ctx, rdsKey, state).Result()
		if err != nil {
			return err
		}

		err = dao.WithContext(ctx).Where("id = ?", change.ID).Update("state", admodel.RedirectStateRunning).Error
		if err != nil {
			return err
		}
	}

	// taskState 任务状态
	taskState, err := rds.HGetAll(ctx, rdsKey).Result()
	if err != nil {
		return err
	}

	userChan := make(chan []model.UserState, 1024)

	current := cast.ToInt(taskState["current"]) // 已经迁移的人数
	total := cast.ToInt(taskState["total"])     // 需要迁移的总人数
	slog.InfoContext(ctx, "redirect_todo", "current", current, "total", total)
	if current >= total {
		return rs.updateResult(ctx, change, admodel.RedirectStateDone)
	}

	go func() {
		left := total - current // 还需要迁移的人数
		rand := 0
		for left > 0 {
			limit := min(200, left)
			users := make([]model.UserState, 0, limit)
			err := mgo.Find(ctx, bson.M{
				"grp_id": change.FromGroupID,
				"rand":   bson.M{"$gt": rand},
			}).Sort("rand").Limit(int64(limit)).All(&users)
			if err != nil {
				time.Sleep(time.Second * 1) // 休眠 1s 后重试
				continue
			}

			if len(users) == 0 {
				break
			}

			userChan <- users
			left -= len(users)
			rand = users[len(users)-1].Rand
		}
		close(userChan)
	}()

	counter := async.NewHashCounter(rds, rdsKey, "current", time.Second)
	defer counter.Close(ctx)

	wg := sync.WaitGroup{}
	for users := range userChan {
		wg.Add(1)
		rs.consumer.Submit(func() {
			defer wg.Done()
			err := rs.UserStateService.moveUIDto(ctx, int(toExp.ProjectID), int(toExp.LayerID), users, change)
			if err != nil {
				time.Sleep(time.Second / 10)
				slog.ErrorContext(ctx, "updateUserStateCluster error", "error", err.Error())
				return
			}

			slog.InfoContext(ctx, "redirect_incr", "incr", len(users))
			counter.IncBy(int64(len(users)))
		})
	}
	wg.Wait()

	return rs.updateResult(ctx, change, admodel.RedirectStateDone)
}

// updateResult 更新结果
func (rs *RedirectService) updateResult(ctx context.Context, change *admodel.SplitGroup, state int32) (err error) {
	// 标记任务状态
	sg := rs.DB.SplitGroup
	_, err = sg.WithContext(ctx).Where(sg.ID.Eq(change.ID)).Update(sg.State, state)

	// 更新 redis 里实时人数
	_, err = rs.UserStateService.CountGrpStateToRedis(ctx, change.ToGroupID)
	if err != nil {
		return
	}
	_, err = rs.UserStateService.CountGrpStateToRedis(ctx, change.FromGroupID)
	if err != nil {
		return
	}

	return
}

func (rs *RedirectService) key(change *admodel.SplitGroup) (key string) {
	return fmt.Sprintf("redirect:%d", change.ID)
}

// AutoRedirectService 收拢自动重定向的逻辑
// @autowire(set=service)
type AutoRedirectService struct{}

func (as *AutoRedirectService) GetConfigForGroup(ctx context.Context, groupID int64) (config *adminmodel.SplitGroup, err error) {
	return
}
