package service

import (
	"testing"

	"github.com/spaolacci/murmur3"
	"github.com/stretchr/testify/assert"
)

func TestHashService_Hash(t *testing.T) {
	hashService := &HashService{}

	tests := []struct {
		name   string
		prefix string
		uid    string
		mod    uint64
		expect uint64
	}{
		{
			name:   "Test case 1",
			prefix: "layer1",
			uid:    "user123",
			mod:    10,
			expect: murmur3.Sum64([]byte("layer1:user123")) % 10,
		},
		{
			name:   "Test case 2",
			prefix: "layer2",
			uid:    "user456",
			mod:    5,
			expect: murmur3.Sum64([]byte("layer2:user456")) % 5,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := hashService.Hash(tt.prefix, tt.uid, tt.mod)
			assert.Equal(t, tt.expect, result)
		})
	}
}
