package service

import (
	"context"

	"git.7k7k.com/data/abScheduler/model"
)

// @autowire(set=service)
type DebugService struct {
	ConfigService    *ConfigService
	UserStateService *UserStateService
	MetricService    *MetricService
}

type ProjectStateIn struct {
	PrjID  int    `json:"prj_id" form:"prj_id"`
	Prj<PERSON>ey string `json:"prj_key" form:"prj_key"`
}
type ProjectStateOut struct {
	Prj               *model.ProjectIdx         `json:"prj"`
	Layers            []*model.LayerIdx         `json:"layers"`
	Exps              []*model.Exp              `json:"exps"`
	UserCount         map[int64]map[int64]int64 `json:"exp_user_count"`          // 分实验、分组的命中人数
	UserCountRealtime map[int64]map[int64]int64 `json:"exp_user_count_realtime"` // 分实验、分组的命中人数
}

func (s *DebugService) ProjectState(ctx context.Context, in ProjectStateIn) (out ProjectStateOut, err error) {
	prj, err := s.ConfigService.GetProject(ctx, in.PrjID, in.PrjKey)
	if err != nil {
		return
	}
	out.Prj = prj

	layers, err := s.ConfigService.GetLayers(ctx, prj.Layers)
	if err != nil {
		return
	}
	out.Layers = layers

	expIds := []int64{}

	for _, layer := range layers {
		var exps map[int]*model.Exp
		exps, err = s.ConfigService.ExpIdx.MGet(ctx, layer.ExpIDs)
		if err != nil {
			return
		}
		for _, exp := range exps {
			out.Exps = append(out.Exps, exp)
			expIds = append(expIds, int64(exp.ID))
		}
	}

	// 进组数统计
	out.UserCount = map[int64]map[int64]int64{}
	out.UserCountRealtime = map[int64]map[int64]int64{}
	if false {
		expUserCount, err := s.MetricService.GetExpUserCount(ctx, expIds)
		if err != nil {
			return out, err
		}
		for expId, count := range expUserCount {
			grpIds := []int64{}

			exp, err := s.ConfigService.ExpIdx.Get(ctx, int(expId))
			if err != nil {
				return out, err
			}

			for _, grpId := range exp.AllGroupIds {
				grpIds = append(grpIds, int64(grpId))
			}

			grpUserCount, err := s.MetricService.GetGrpUserCount(ctx, grpIds)
			if err != nil {
				return out, err
			}
			grpUserCount[0] = count
			out.UserCount[expId] = grpUserCount

			expUserCountRealtime, err := s.MetricService.RealtimeGetGroupCount(ctx, grpIds)
			if err != nil {
				return out, err
			}
			// expUserCountRealtime[0] = count
			out.UserCountRealtime[expId] = expUserCountRealtime
		}
	}

	return
}
