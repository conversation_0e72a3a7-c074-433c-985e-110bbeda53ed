package service

import (
	"context"
	"fmt"
	"time"

	"git.7k7k.com/data/abScheduler/infra/metric"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/data/abScheduler/model"
	"github.com/cockroachdb/errors"
	"github.com/redis/go-redis/v9"
)

// @autowire(set=service)
type MetricService struct {
	RedisClts *redises.ClientMgr

	updatedAt time.Time `wire:"-"`
}

// GetGrpUserCount 批量获取指定实验组的用户数
func (s *MetricService) GetGrpUserCount(ctx context.Context, grpIds []int64) (map[int64]int64, error) {
	pipe := s.RedisClts.AdminRedis.Pipeline()
	cmds := make(map[int64]*redis.IntCmd, len(grpIds))

	// 将所有命令添加到pipeline
	for _, grpId := range grpIds {
		key := fmt.Sprintf("pf_g_u:g%d", grpId)
		cmds[grpId] = pipe.PFCount(ctx, key)
	}

	// 执行pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, err
	}

	// 收集结果
	result := make(map[int64]int64, len(grpIds))
	for grpId, cmd := range cmds {
		count, err := cmd.Result()
		if err != nil {
			return nil, err
		}
		result[grpId] = count
	}

	return result, nil
}

// GetExpUserCount 批量获取指定实验的用户数
func (s *MetricService) GetExpUserCount(ctx context.Context, expIds []int64) (map[int64]int64, error) {
	pipe := s.RedisClts.AdminRedis.Pipeline()
	cmds := make(map[int64]*redis.IntCmd, len(expIds))

	// 将所有命令添加到pipeline
	for _, expId := range expIds {
		key := fmt.Sprintf("pf_e_u:e%d", expId)
		cmds[expId] = pipe.PFCount(ctx, key)
	}

	// 执行pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, err
	}

	// 收集结果
	result := make(map[int64]int64, len(expIds))
	for expId, cmd := range cmds {
		count, err := cmd.Result()
		if err != nil {
			return nil, err
		}
		result[expId] = count
	}

	return result, nil
}

// SetRedisStats 写入 redis 统计数据 国内
func (s *MetricService) SetRedisStats(ctx context.Context, states []model.UserState) (err error) {
	cli := s.RedisClts.AdminRedis
	pipe := cli.Pipeline()
	for _, state := range states {
		// key 不要变，admin 也会读
		pipe.PFAdd(ctx, fmt.Sprintf("pf_g_u:g%d", state.GrpId), state.UID)
		pipe.PFAdd(ctx, fmt.Sprintf("pf_e_u:e%d", state.ExpId), state.UID)
	}
	_, err = pipe.Exec(ctx)
	if err != nil {
		return errors.Wrapf(err, "SetRedisStats")
	}

	return nil
}

// SetRedisRealtime 更新实时数据
func (s *MetricService) RealtimeSetGroup(ctx context.Context, grpCount map[int64]int64, ttl time.Duration) (err error) {
	cli := s.RedisClts.AdminRedis
	pipe := cli.Pipeline()
	for grpId, count := range grpCount {
		pipe.Set(ctx, fmt.Sprintf("real_g_u:%d", grpId), count, ttl)
	}
	_, err = pipe.Exec(ctx)
	if err != nil {
		return errors.Wrapf(err, "RealtimeSetGroup")
	}

	return
}

// RealtimeSetExp 更新实时数据
func (s *MetricService) RealtimeSetExp(ctx context.Context, expCount map[int64]int64, ttl time.Duration) (err error) {
	cli := s.RedisClts.AdminRedis
	pipe := cli.Pipeline()
	for expId, count := range expCount {
		pipe.Set(ctx, fmt.Sprintf("real_e_u:%d", expId), count, ttl)
	}
	_, err = pipe.Exec(ctx)
	if err != nil {
		return errors.Wrapf(err, "RealtimeSetExp")
	}

	return
}

// RealtimeGetGroupCount 获取实时数据
func (s *MetricService) RealtimeGetGroupCount(ctx context.Context, grpIds []int64) (grpCount map[int64]int64, err error) {
	cli := s.RedisClts.AdminRedis
	pipe := cli.Pipeline()
	cmds := make(map[int64]*redis.StringCmd, len(grpIds))

	for _, grpId := range grpIds {
		cmds[grpId] = pipe.Get(ctx, fmt.Sprintf("real_g_u:%d", grpId))
	}

	_, err = pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, errors.Wrapf(err, "RealtimeGetGroupCount")
	}

	grpCount = make(map[int64]int64, len(grpIds))
	for grpId, cmd := range cmds {
		val, err := cmd.Int64()
		if err == nil && val > 0 {
			grpCount[grpId] = val
		}
	}

	return grpCount, nil
}

type RequestLabel struct {
	ProjectKey string
	UID        string
	Country    string
}

// TouchRequest 请求基本打点
func (s *MetricService) TouchRequest(ctx context.Context, req *RequestLabel) (err error) {
	if req.Country == "" {
		req.Country = "-"
	}

	// QPS
	{

	}

	// DAU
	{
		cli := s.RedisClts.BaseRedis
		pipe := cli.Pipeline()
		keyDAU := fmt.Sprintf("pfp:%s:%s:dau:%s", req.ProjectKey, req.Country, time.Now().Format("20060102"))
		{ // DAU
			pipe.PFAdd(ctx, keyDAU, req.UID)
			pipe.Expire(ctx, keyDAU, time.Hour*24)
		}
		_, err = pipe.Exec(ctx)
		if err != nil {
			return errors.Wrapf(err, "MetricTouchRequest")
		}

		if s.update() {
			cnt, _ := s.PFCount(ctx, cli, keyDAU)
			metric.ProjectDAU.WithLabelValues(req.ProjectKey, req.Country).Set(float64(cnt))
		}
	}
	return
}

func (s *MetricService) PFCount(ctx context.Context, cli redis.UniversalClient, keys ...string) (count int64, err error) {
	count, err = cli.PFCount(ctx, keys...).Result()
	if err != nil {
		return 0, errors.Wrapf(err, "PFCount")
	}
	return count, nil
}

func (s *MetricService) update() bool {
	if now := time.Now(); now.After(s.updatedAt) {
		s.updatedAt = now.Add(time.Second * 5)
		return true
	}
	return false
}
