linters:
  disable-all: true
  enable:
    # 重要
    - govet # 重要：官方代码检查工具，检测变量作用域/格式化等基础问题
    - staticcheck # 重要：高级静态分析，检测无效代码/错误API使用等
    # - errcheck # 重要：检查未处理的错误返回。关闭，原因是 defer Close() 一般不会检查
    - typecheck # 重要：编译器级类型检查
    - gocyclo # 重要：检查函数复杂度
    
    # 代码风格检查
    - gofmt
    - goimports
    - gosimple
    # - revive
    - gocritic  # 功能：注释前空格
    - misspell  # 功能：拼写检查

    # default linter
    # - ineffassign
    # - unused
    # 新增 linter
    - dupword
    - gofumpt
    
    # 代码风格检查
    - goconst
    # - stylecheck
    
    # 代码质量检查
    - gosec
    - errorlint
    - bodyclose
    - nilerr
    
    # 性能相关
    - prealloc

linters-settings:
  govet:
    shadow: true  # 变量遮蔽检查

  gocyclo:
    min-complexity: 20
    
  gosec:
    excludes:
      - G104
      - G115
      - G404
  
  errorlint:
    errorf: true

issues:
  exclude-use-default: false  # 禁用默认排除规则
  max-issues-per-linter: 0    # 不限制每个linter的报告数量
  max-same-issues: 0          # 不限制相同问题的报告数量

output:
  color: always     # 命令行输出高亮
  formats: colored-line-number