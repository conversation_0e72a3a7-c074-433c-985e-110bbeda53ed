package main

import (
	"context"
	"net/url"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

func main() {
	pass := url.QueryEscape("3mGX4UyUAlSh09~:Vc.kBn#NP_It")
	pass = url.QueryEscape("hU.SfCl5#K]H(jis|SF8.J[R02.9")
	uri := "mongodb://abtest:" + pass + "@sjpt-abtest-db-2412.cluster-cjmimqdxiiih.us-east-2.docdb.amazonaws.com:27017/?tls=true&tlsCAFile=/data/service/keys/global-bundle.pem&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
	clt, err := mongo.Connect(context.Background(), options.Client().ApplyURI(uri))
	if err != nil {
		panic(err)
	}

	err = clt.Ping(context.Background(), readpref.Primary())
	if err != nil {
		panic(err)
	}
}
