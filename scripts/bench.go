package main

import (
	"bytes"
	"flag"
	"fmt"
	"io"
	"math/rand/v2"
	"net/http"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

type RequestConfig struct {
	method      string
	url         string
	body        string      // JSON 字符串模板
	headers     [][2]string // [key, value] 对数组
	uidTemplate string      // 支持 {uid} 占位符
	verbose     bool        // 添加 verbose 字段用于控制日志输出
}

type BenchmarkResult struct {
	totalRequests uint64
	successCount  uint64
	failureCount  uint64
	duration      time.Duration
	latencies     []time.Duration // 存储所有请求的延迟
	latencyLock   sync.Mutex      // 用于保护 latencies 切片
	errorLogs     []string        // 添加错误日志存储
	errorLogLock  sync.Mutex      // 用于保护错误日志
}

func generateRandomUID(prefix string, length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.IntN(len(charset))]
	}
	return prefix + string(b)
}

func parseHeaders(headerStr string) [][2]string {
	if headerStr == "" {
		return [][2]string{
			{"Content-Type", "application/json"},
			{"User-Agent", "Go-Benchmark-Script/1.0"},
		}
	}

	pairs := strings.Split(headerStr, ",")
	headers := make([][2]string, 0, len(pairs))
	for _, pair := range strings.Split(headerStr, ",") {
		kv := strings.SplitN(pair, ":", 2)
		if len(kv) == 2 {
			headers = append(headers, [2]string{
				strings.TrimSpace(kv[0]),
				strings.TrimSpace(kv[1]),
			})
		}
	}
	return headers
}

func sendRequest(client *http.Client, config RequestConfig, result *BenchmarkResult) (bool, time.Duration) {
	startTime := time.Now()

	bodyStr := config.body
	if strings.Contains(bodyStr, "{uid}") {
		uid := generateRandomUID(config.uidTemplate, 4)
		bodyStr = strings.ReplaceAll(bodyStr, "{uid}", uid)
	}

	req, err := http.NewRequest(config.method, config.url, bytes.NewBufferString(bodyStr))
	if err != nil {
		if config.verbose {
			result.errorLogLock.Lock()
			result.errorLogs = append(result.errorLogs, fmt.Sprintf("创建请求失败: %v", err))
			result.errorLogLock.Unlock()
		}
		return false, time.Since(startTime)
	}

	// 设置请求头
	for _, h := range config.headers {
		req.Header.Set(h[0], h[1])
	}

	resp, err := client.Do(req)
	if err != nil {
		if config.verbose {
			result.errorLogLock.Lock()
			result.errorLogs = append(result.errorLogs, fmt.Sprintf("发送请求失败: %v", err))
			result.errorLogLock.Unlock()
		}
		return false, time.Since(startTime)
	}
	defer resp.Body.Close()

	// 读取并丢弃响应体内容，确保TCP连接可以被复用
	_, err = io.Copy(io.Discard, resp.Body)
	if err != nil && config.verbose {
		result.errorLogLock.Lock()
		result.errorLogs = append(result.errorLogs, fmt.Sprintf("读取响应体失败: %v", err))
		result.errorLogLock.Unlock()
	}

	if resp.StatusCode != 200 && config.verbose {
		result.errorLogLock.Lock()
		result.errorLogs = append(result.errorLogs, fmt.Sprintf("请求返回非200状态码: %d", resp.StatusCode))
		result.errorLogLock.Unlock()
	}

	return resp.StatusCode == 200, time.Since(startTime)
}

func worker(client *http.Client, duration time.Duration, config RequestConfig, wg *sync.WaitGroup, result *BenchmarkResult) {
	defer wg.Done()
	endTime := time.Now().Add(duration)

	for time.Now().Before(endTime) {
		success, latency := sendRequest(client, config, result)
		atomic.AddUint64(&result.totalRequests, 1)
		if success {
			atomic.AddUint64(&result.successCount, 1)
		} else {
			atomic.AddUint64(&result.failureCount, 1)
		}

		result.latencyLock.Lock()
		result.latencies = append(result.latencies, latency)
		result.latencyLock.Unlock()
	}
}

// 添加一个计算分位数的辅助函数
func calculatePercentile(latencies []time.Duration, p float64) time.Duration {
	if len(latencies) == 0 {
		return 0
	}

	// 创建副本并排序
	sorted := make([]time.Duration, len(latencies))
	copy(sorted, latencies)
	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i] < sorted[j]
	})

	index := int(float64(len(sorted)-1) * p)
	return sorted[index]
}

// 表格打印辅助结构
type Table struct {
	buffer    []string
	maxWidth  int
	leftChar  string
	rightChar string
}

// 创建新表格
func NewTable() *Table {
	return &Table{
		buffer:    make([]string, 0),
		maxWidth:  0,
		leftChar:  "  ",
		rightChar: "  ",
	}
}

// 添加行内容
func (t *Table) AddLine(format string, args ...interface{}) {
	line := fmt.Sprintf(format, args...)
	t.buffer = append(t.buffer, line)
	if len(line) > t.maxWidth {
		t.maxWidth = len(line)
	}
}

// 添加分隔线
func (t *Table) AddSeparator(first, middle, last string) {
	width := t.maxWidth + len(t.leftChar) + len(t.rightChar)
	line := strings.Repeat("─", width)
	t.buffer = append(t.buffer, line)
}

// 获取格式化后的表格
func (t *Table) String() string {
	var result strings.Builder
	for _, line := range t.buffer {
		if strings.Contains(line, "─") {
			result.WriteString(line + "\n")
			continue
		}

		content := strings.TrimRight(line, " ")
		padding := strings.Repeat(" ", t.maxWidth-len(content))
		result.WriteString(t.leftChar + content + padding + t.rightChar + "\n")
	}
	return result.String()
}

func printResult(result *BenchmarkResult, startTime time.Time) {
	duration := time.Since(startTime)
	total := atomic.LoadUint64(&result.totalRequests)
	success := atomic.LoadUint64(&result.successCount)
	failed := atomic.LoadUint64(&result.failureCount)

	// 计算延迟统计
	result.latencyLock.Lock()
	latencies := make([]time.Duration, len(result.latencies))
	copy(latencies, result.latencies)
	result.latencyLock.Unlock()

	var avgLatency time.Duration
	var maxLatency time.Duration
	minLatency := time.Hour // 初始化为一个足够大的值
	if len(latencies) > 0 {
		var sum time.Duration
		for _, lat := range latencies {
			sum += lat
			if lat > maxLatency {
				maxLatency = lat
			}
			if lat < minLatency {
				minLatency = lat
			}
		}
		avgLatency = sum / time.Duration(len(latencies))
	}

	// 计算分位数
	p50 := calculatePercentile(latencies, 0.50)
	p90 := calculatePercentile(latencies, 0.90)
	p95 := calculatePercentile(latencies, 0.95)
	p99 := calculatePercentile(latencies, 0.99)

	// 创建表格
	table := NewTable()

	// 添加表头
	table.AddSeparator("┌", "─", "┐")
	table.AddLine("性能测试报告 (运行时间: %.1f 秒)", duration.Seconds())
	table.AddSeparator("├", "─", "┤")

	// 请求统计
	table.AddLine("请求统计:")
	table.AddLine("  总请求数: %-8d    成功: %-8d    失败: %-4d", total, success, failed)
	table.AddLine("  成功率: %6.2f%%      当前 QPS: %-8.2f",
		float64(success)/float64(total)*100,
		float64(total)/duration.Seconds())

	// 延迟统计
	table.AddSeparator("├", "─", "┤")
	table.AddLine("延迟统计:")
	table.AddLine("  最小延迟: %-10.3fms   平均延迟: %-10.3fms",
		float64(minLatency.Microseconds())/1000,
		float64(avgLatency.Microseconds())/1000)
	table.AddLine("  最大延迟: %-10.3fms",
		float64(maxLatency.Microseconds())/1000)
	table.AddLine("")
	table.AddLine("延迟分位数:")
	table.AddLine("  P50: %-10.3fms   P90: %-10.3fms",
		float64(p50.Microseconds())/1000,
		float64(p90.Microseconds())/1000)
	table.AddLine("  P95: %-10.3fms   P99: %-10.3fms",
		float64(p95.Microseconds())/1000,
		float64(p99.Microseconds())/1000)
	table.AddSeparator("└", "─", "┘")

	// 打印最近的错误日志
	result.errorLogLock.Lock()
	if len(result.errorLogs) > 0 {
		table.AddSeparator("├", "─", "┤")
		table.AddLine("最近错误日志:")
		// 只显示最近的10条错误日志
		start := 0
		if len(result.errorLogs) > 10 {
			start = len(result.errorLogs) - 10
		}
		for _, log := range result.errorLogs[start:] {
			table.AddLine("  %s", log)
		}
	}
	result.errorLogLock.Unlock()

	// 清屏并打印表格
	fmt.Print("\033[H\033[2J")
	fmt.Print(table.String())
}

func runBenchmark(concurrentUsers int, duration time.Duration, config RequestConfig) *BenchmarkResult {
	result := BenchmarkResult{}
	var wg sync.WaitGroup

	client := &http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        concurrentUsers,
			MaxIdleConnsPerHost: concurrentUsers,
			IdleConnTimeout:     90 * time.Second,
		},
		Timeout: 10 * time.Second,
	}

	startTime := time.Now()

	// 启动定期打印结果的 goroutine
	stopPrinting := make(chan struct{})
	go func() {
		ticker := time.NewTicker(1 * time.Second / 2)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				printResult(&result, startTime)
			case <-stopPrinting:
				return
			}
		}
	}()

	for i := 0; i < concurrentUsers; i++ {
		wg.Add(1)
		go worker(client, duration, config, &wg, &result)
	}

	wg.Wait()
	close(stopPrinting)

	result.duration = time.Since(startTime)

	// 打印最终结果
	table := NewTable()
	table.AddSeparator("┌", "─", "┐")
	table.AddLine("最终测试报告")
	table.AddSeparator("├", "─", "┤")
	table.AddLine("测试配置:")
	table.AddLine("  并发用户数: %-6d   测试时长: %.1f 秒",
		concurrentUsers, result.duration.Seconds())
	table.AddLine("  目标 URL: %s", truncateString(config.url, 45))
	table.AddLine("  请求方法: %s", config.method)
	table.AddSeparator("├", "─", "┤")
	table.AddLine("总体统计:")
	table.AddLine("  总请求数: %-10d  成功: %-10d  失败: %-10d",
		result.totalRequests, result.successCount, result.failureCount)
	table.AddLine("  平均 QPS: %-10.2f  成功率: %.2f%%",
		float64(result.totalRequests)/result.duration.Seconds(),
		float64(result.successCount)/float64(result.totalRequests)*100)
	table.AddSeparator("└", "─", "┘")

	fmt.Print("\n" + table.String())

	return &result
}

// 添加一个辅助函数来截断过长的字符串
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s + strings.Repeat(" ", maxLen-len(s))
	}
	return s[:maxLen-3] + "..."
}

/*
	go run scripts/bench.go \
	  -c 16 \
	  -t 600 \
	  -m POST \
	  -u http://************:7020/abtest/traffic \
	  -d '{"projectKey":"zhr","uid":"{uid}"}' \
	  -p bench_
*/
func main() {
	concurrent := flag.Int("c", 0, "并发用户数")
	timeSeconds := flag.Int("t", 0, "测试持续时间(秒)")
	method := flag.String("m", "", "HTTP方法")
	url := flag.String("u", "", "目标URL")
	body := flag.String("d", "", "请求体JSON模板")
	headers := flag.String("H", "", "请求头(格式: Key1:Value1,Key2:Value2)")
	uidPrefix := flag.String("p", "", "UID前缀")
	verbose := flag.Bool("ve", false, "是否打印详细错误日志")
	flag.Parse()

	// 检查必需参数
	if *concurrent == 0 || *timeSeconds == 0 || *url == "" || *method == "" || *body == "" {
		flag.Usage()
		return
	}

	config := RequestConfig{
		method:      *method,
		url:         *url,
		body:        *body,
		headers:     parseHeaders(*headers),
		uidTemplate: *uidPrefix,
		verbose:     *verbose,
	}

	// 使用表格打印初信息
	table := NewTable()
	table.AddSeparator("┌", "─", "┐")
	table.AddLine("压力测试开始")
	table.AddSeparator("├", "─", "┤")
	table.AddLine("并发用户数: %d", *concurrent)
	table.AddLine("持续时间: %d 秒", *timeSeconds)
	table.AddLine("请求方法: %s", config.method)
	table.AddLine("目标 URL: %s", truncateString(config.url, 45))
	table.AddLine("请求体模板: %s", truncateString(config.body, 45))
	table.AddLine("详细日志: %v", config.verbose)
	table.AddSeparator("└", "─", "┘")

	fmt.Print(table.String())
	fmt.Println("\n按 Ctrl+C 停止测试...")

	runBenchmark(*concurrent, time.Duration(*timeSeconds)*time.Second, config)
}
