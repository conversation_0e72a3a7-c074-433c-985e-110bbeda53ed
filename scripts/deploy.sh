#!/bin/bash

# 停止现有服务
# sudo systemctl stop abScheduler || true

# 复制服务文件
sudo cp scripts/abScheduler.service /etc/systemd/system/

# 安装 logrotate 配置
sudo cp scripts/abScheduler.logrotate /etc/logrotate.d/
sudo chown root:root /etc/logrotate.d/abScheduler.logrotate
sudo chmod 644 /etc/logrotate.d/abScheduler.logrotate

# 重新加载 systemd
sudo systemctl daemon-reload

# 启动服务
sudo systemctl reload abScheduler

# 设置开机自启
sudo systemctl enable abScheduler

# 检查服务状态
sudo systemctl status abScheduler
