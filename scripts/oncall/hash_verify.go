package oncall

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"git.7k7k.com/data/abScheduler/gopkg/stat"
	"git.7k7k.com/data/abScheduler/infra"
	"git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/pkg/common/csvutil"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

func readBucketState() []model.Bucket {
	b, err := os.ReadFile("/Users/<USER>/Downloads/bucket_state.json")
	if err != nil {
		panic(err)
	}

	var buckets []model.Bucket
	err = json.Unmarshal(b, &buckets)
	if err != nil {
		panic(err)
	}

	return buckets
}

func ReviewHash383(ctx context.Context, app *infra.Application) {
	bucketState := readBucketState()
	if len(bucketState) == 0 {
		panic("buckets is empty")
	}
	// fmt.Println("bucketState", bucketState)

	rows, err := csvutil.CSVReadFromFile("/Users/<USER>/Downloads/383.csv")
	if err != nil {
		panic(err)
	}

	s := app.HashService

	buckets := make([]int, 1000)
	groups := make(map[int]int)
	for _, row := range rows {
		uid := row["uid"] + row["distinct_id"]
		idx := s.HashUserInLayer(uid, &model.LayerIdx{ID: 7, Mod: 1000})
		// idx := s.Hash("7", uid, 1000)
		buckets[idx]++
		groups[bucketState[idx].GroupId]++
		// fmt.Println(uid)
		// fmt.Println(idx)
		// fmt.Println(bucketState[idx].GroupId)
	}
	fmt.Println("总样本量", len(rows))
	fmt.Println("方差\tP-Value\tcv")
	fmt.Println(stat.UniformityTest(buckets, len(rows)))

	delete(groups, 0)
	fmt.Println(groups)
	group2 := lo.Values(groups)
	fmt.Println("rows", len(rows))
	fmt.Println("chiSquare, pValue, cv")
	fmt.Println(stat.UniformityTest(group2, len(rows)))
}
func reviewHash2(ctx context.Context, app *infra.Application) {
	rows, err := csvutil.CSVReadFromFile("/Users/<USER>/Downloads/383_cnt.csv")
	if err != nil {
		panic(err)
	}

	total := 0
	cnt := make([]int, 0)
	for _, row := range rows {
		v := cast.ToInt(row["count_uid"])
		cnt = append(cnt, v)
		total += v
	}

	fmt.Println("total", total, cnt)
	fmt.Println("chiSquare, pValue, cv")
	fmt.Println(stat.UniformityTest(cnt, len(rows)))
}

// testHashResult 检验线上hash结果跟本地一致性
func testHashResult(ctx context.Context, app *infra.Application) {
	bucketState := readBucketState()
	if len(bucketState) == 0 {
		panic("buckets is empty")
	}

	rows, err := csvutil.CSVReadFromFile("/Users/<USER>/Downloads/uid_result.csv")
	if err != nil {
		panic(err)
	}

	s := app.HashService

	for _, row := range rows {
		uid := row["uid"]
		want := row["_col1"]
		idx := s.Hash("7", uid, 1000)
		got := bucketState[idx].GroupId
		fmt.Println(uid, want, got)
	}
}
