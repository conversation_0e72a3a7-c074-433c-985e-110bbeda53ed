[Unit]
Description=AB分流服务
After=network.target

[Service]
Type=notify
NotifyAccess=all
User=root
WorkingDirectory=/data/service/abScheduler
ExecStart=/data/service/abScheduler/abScheduler -c resource/config/config.toml
ExecReload=/bin/kill -USR2 $MAINPID
Restart=on-failure
RestartSec=3
StandardOutput=append:/data/logs/abScheduler/stdout.log
StandardError=append:/data/logs/abScheduler/stderr.log

[Install]
WantedBy=multi-user.target
