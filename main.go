package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"log/slog"
	"os"
	"runtime"
	"runtime/debug"
	"strconv"
	"time"

	"git.7k7k.com/data/abScheduler/autowire"
	"git.7k7k.com/data/abScheduler/infra"
	"git.7k7k.com/data/abScheduler/infra/config"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/pkg/common/ctxs"
	"git.7k7k.com/pkg/common/logs"
	"git.7k7k.com/pkg/common/metric"
	"git.7k7k.com/pkg/storage"
	"github.com/Kretech/xgo/dump"
	"github.com/coreos/go-systemd/v22/daemon"
	"github.com/getsentry/sentry-go"
	"github.com/robfig/cron/v3"
	zerolog "github.com/samber/slog-zerolog/v2"
)

var GIT_HEAD string

func init() {
	ok, err := daemon.SdNotify(false, "MAINPID="+strconv.Itoa(os.Getpid()))
	_, reload := os.LookupEnv("LISTEN_FDS")
	log.Println("MAINPID=", os.Getpid(), "reload", reload, "Notify systemd", ok, err)
	if !reload {
		ok, err := daemon.SdNotify(false, "READY=1")
		log.Println("Sent READY", ok, err)
	}
}

func init() {
	storage.Init("abtest_scheduler")
	metric.Init("abtest_scheduler")

	logs.OptsLocationShortFile = true
	logs.InitOpts(os.Stdout, func(opts *zerolog.Option) {
		opts.Level = slog.LevelInfo
	}, true)

	dump.Disable = runtime.GOOS != `darwin`
}

func main() {
	configPath := flag.String("c", "./resource/config/config_local.toml", "config path")
	port := flag.Int("p", 0, "port")
	flag.Parse()

	args := flag.Args()

	if len(args) == 0 {
		args = append(args, "serve")
	}

	config.SetGlobalConfig(*configPath)

	app, cleanup, err := autowire.InitializeApplication()
	if err != nil {
		panic(err)
	}
	defer cleanup()
	slog.Info("InitializeApplication " + time.Since(infra.Process.StartedAt).String())

	flush := SentryInit(app.Config.Sentry)
	defer flush(2 * time.Second)
	slog.Info("SentryInit " + time.Since(infra.Process.StartedAt).String())

	if *port != 0 {
		app.Config.Addr = fmt.Sprintf(":%d", *port)
	}

	if os.Getenv("DISABLE_CRON") != "1" {
		startRunner(app)
	}

	switch args[0] {
	case "version":
		fmt.Println(GIT_HEAD)
	case "runcode":
		runCode(app)
	case "serve":
		app.RunHTTP()
	default:
		flag.PrintDefaults()
	}
}

func startRunner(app *infra.Application) {
	// clog := &logs.CronLogger{EntryName: make(map[int]string)}
	c := cron.New(
		cron.WithSeconds(),
		// cron.WithLogger(log),
		cron.WithChain(
			func(j cron.Job) cron.Job {
				return cron.FuncJob(func() {
					defer func() {
						if err := recover(); err != nil {
							debug.PrintStack()
							slog.Error("cron_panic: " + string(debug.Stack()))
							sentry.CurrentHub().Recover(err)
						}
					}()

					j.Run()
				})
			},
		),
	)

	// 10min 全量更新数据
	entryId, _ := c.AddFunc("0 * * * * *", redises.WithLock(app.Redis.BaseRedis, "SyncConfig", time.Minute, func(ctx context.Context) error {
		return app.DataTransfer.RefreshAll(ctx, "cron", nil)
	}))
	// clog.SetEntryName(int(entryId), "SyncConfig")

	// 每1s 监听 DB 变化，定向更新数据
	entryId, _ = c.AddFunc("* * * * * *", redises.WithLock(app.Redis.DefaultRedis, "watchDBChanged", time.Second*3, func(ctx context.Context) error {
		app.DataTransfer.WatchDBChange(ctx)
		return nil
	}))
	// clog.SetEntryName(int(entryId), "watchDBChanged")

	// 重定向
	entryId, _ = c.AddFunc("* * * * * *", redises.WithLock(app.Redis.DefaultRedis, "ListenRedirect", time.Second, app.RedirectService.RunOnce))
	// clog.SetEntryName(int(entryId), "ListenRedirect")

	// 更新组实时人数到 redis
	entryId, _ = c.AddFunc("0 15,45 * * * *", redises.WithLock(app.Redis.DefaultRedis, "ReCountStatsToRedis", time.Hour, app.UserStateService.ReCountStatsToRedis))
	// clog.SetEntryName(int(entryId), "ReCountStatsToRedis")

	// 更新 MongoDB 密码
	entryId, _ = c.AddFunc("0 0 4 * * *", redises.WithLock(app.Redis.BaseRedis, "MongoSetReloadDB", time.Minute, func(ctx context.Context) error {
		app.MongoSet.InitDB(false)
		return nil
	}))
	// clog.SetEntryName(int(entryId), "MongoSetReloadDB")

	// 清理 Redis
	c.AddFunc("0 0 9 * * *", redises.WithLock(app.Redis.DefaultRedis, "CleanRecentExp", time.Minute, func(ctx context.Context) error {
		return app.CleanService.FindClosedAndRemoveCache(ctx, time.Now().Add(-time.Hour*24))
	}))

	// 清理过期用户状态
	c.AddFunc("50 * * * * *", redises.WithLock(app.Redis.DefaultRedis, "CleanExpiredUserStates", time.Minute*30, func(ctx context.Context) error {
		return app.UserStateService.CleanExpiredUserStates(ctx)
	}))

	_ = entryId

	c.Start()
}

// DISABLE_MQ=1 DISABLE_CRON=1 GIN_MODE=release ./abScheduler -c=resource/config/config.toml runcode
func runCode(app *infra.Application) {
	ctx := context.WithValue(context.Background(), ctxs.KeyTraceID, logs.GenerateTraceID())
	slog.InfoContext(ctx, "runCode")
	defer slog.InfoContext(ctx, "runCode done")

	log.SetOutput(os.Stderr)
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	// oncall.AnalysisIDLETIME(ctx, app)

	// app.CleanService.FindClosedAndRemoveCache(ctx, time.Unix(0, 0))

}

func ReCountStatsToRedis(app *infra.Application, ctx context.Context) {
	err := app.UserStateService.ReCountStatsToRedis(ctx)
	if err != nil {
		slog.Error("ReCountStatsToRedis", "err", err)
	}

	slog.InfoContext(ctx, "ReCountStatsToRedis_done")
}

func clean(app *infra.Application) {
	ctx := context.WithValue(context.Background(), ctxs.KeyTraceID, logs.GenerateTraceID())
	err := app.CleanService.FindClosedAndRemoveCache(ctx, time.Unix(0, 0))
	if err != nil {
		slog.Error("CleanRecentExp", "err", err)
	}
	slog.InfoContext(ctx, "CleanRecentExp done")
}
