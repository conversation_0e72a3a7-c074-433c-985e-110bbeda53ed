// Code generated by go-autowire. DO NOT EDIT.

//go:build wireinject
// +build wireinject

package autowire

import (
	"github.com/google/wire"

	"git.7k7k.com/data/abScheduler/infra"
	"git.7k7k.com/data/abScheduler/infra/config"
	"git.7k7k.com/data/abScheduler/infra/redises"
)

var InfraSet = wire.NewSet(
	wire.Struct(new(infra.Container), "*"),

	infra.NewApplication,

	config.LoadGlobalConfig,

	redises.NewClientMgr,

	redises.NewDefaultClient,

	redises.NewRedsync,
)
