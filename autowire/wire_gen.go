// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package autowire

import (
	"git.7k7k.com/data/abScheduler/httpd"
	"git.7k7k.com/data/abScheduler/infra"
	"git.7k7k.com/data/abScheduler/infra/config"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/data/abScheduler/repository"
	"git.7k7k.com/data/abScheduler/repository/mongos"
	"git.7k7k.com/data/abScheduler/repository/mysql"
	"git.7k7k.com/data/abScheduler/service"
	"git.7k7k.com/data/abScheduler/service/abtest"
)

// Injectors from wire.gen.go:

func InitializeApplication() (*infra.Application, func(), error) {
	configConfig, err := config.LoadGlobalConfig()
	if err != nil {
		return nil, nil, err
	}
	clientMgr := redises.NewClientMgr(configConfig)
	notifyService := service.NewNotifyService(clientMgr)
	configService := service.NewConfigService(clientMgr, notifyService)
	mongoSet := mongos.NewMongos(configConfig)
	db := repository.InitDefaultDB(configConfig)
	userStateRepo := mysql.NewUserStateRepo(db)
	metricService := &service.MetricService{
		RedisClts: clientMgr,
	}
	userHistoryService := service.NewUserHistoryService(mongoSet)
	userStateService, cleanup := service.NewUserStateService(configConfig, clientMgr, mongoSet, userStateRepo, configService, notifyService, metricService, userHistoryService)
	userLabelService := &service.UserLabelService{
		RedisClts:          clientMgr,
		UserStateService:   userStateService,
		UserHistoryService: userHistoryService,
	}
	whiteCombService := service.NewWhiteCombService(clientMgr, configService)
	rateService := service.NewRateService(clientMgr)
	hashService := &service.HashService{}
	distributeService := &abtest.DistributeService{
		ConfigService:    configService,
		UserStateService: userStateService,
		UserLabelService: userLabelService,
		WhiteCombService: whiteCombService,
		RateService:      rateService,
		MetricService:    metricService,
		HashService:      hashService,
	}
	abTestHandler := httpd.AbTestHandler{
		AbtestService: distributeService,
	}
	queryAdmin := repository.InitAdminQuery(configConfig)
	configMetaStore := service.NewConfigMetaStore(clientMgr)
	universalClient := redises.NewDefaultClient(clientMgr)
	redsync := redises.NewRedsync(universalClient)
	dataTransfer := &service.DataTransfer{
		DB:            queryAdmin,
		Store:         configMetaStore,
		ConfigService: configService,
		Redsync:       redsync,
		RedisClients:  clientMgr,
	}
	autoRedirectService := service.AutoRedirectService{}
	redirectService := &service.RedirectService{
		DB:                  queryAdmin,
		Redsync:             redsync,
		DefaultRedis:        universalClient,
		UserStateService:    userStateService,
		AutoRedirectService: autoRedirectService,
	}
	cleanService := &service.CleanService{
		DB:               queryAdmin,
		ConfigService:    configService,
		UserStateService: userStateService,
		RedisClients:     clientMgr,
	}
	userLeaveService := &service.UserLeaveService{
		ConfigService:    configService,
		UserStateService: userStateService,
	}
	debugService := &service.DebugService{
		ConfigService:    configService,
		UserStateService: userStateService,
		MetricService:    metricService,
	}
	handlers := &httpd.Handlers{
		RedisMgr:         clientMgr,
		AbTestHandler:    abTestHandler,
		AbtestService:    distributeService,
		DataTransfer:     dataTransfer,
		UserStateService: userStateService,
		ConfigService:    configService,
		RedirectService:  redirectService,
		WhiteCombService: whiteCombService,
		CleanService:     cleanService,
		UserLeaveService: userLeaveService,
		DebugService:     debugService,
		HashService:      hashService,
	}
	server, cleanup2 := httpd.NewHTTPServer(configConfig, handlers)
	httpdHandlers := httpd.Handlers{
		RedisMgr:         clientMgr,
		AbTestHandler:    abTestHandler,
		AbtestService:    distributeService,
		DataTransfer:     dataTransfer,
		UserStateService: userStateService,
		ConfigService:    configService,
		RedirectService:  redirectService,
		WhiteCombService: whiteCombService,
		CleanService:     cleanService,
		UserLeaveService: userLeaveService,
		DebugService:     debugService,
		HashService:      hashService,
	}
	container := &infra.Container{
		Config:   configConfig,
		Redis:    clientMgr,
		HTTP:     server,
		Handlers: httpdHandlers,
		MongoSet: mongoSet,
	}
	application, cleanup3, err := infra.NewApplication(container)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	return application, func() {
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
