package main

import (
	"log/slog"
	"time"

	"git.7k7k.com/pkg/common/gosentry"
	"github.com/getsentry/sentry-go"
)

func SentryInit(c gosentry.SentryConfig) (flush func(timeout time.Duration) bool) {
	err := sentry.Init(sentry.ClientOptions{
		Dsn:              c.DSN,
		Environment:      c.Env,
		AttachStacktrace: true,
		EnableTracing:    true,
		TracesSampleRate: c.TracesSampleRate,
		// Debug:            c.Env == "local",
	})
	if err != nil {
		panic("sentry.Init: " + err.Error())
	}

	go func() {
		for range time.Tick(time.Second * 1) {
			beginAt := time.Now()
			flush := sentry.Flush(2 * time.Second)
			cost := time.Since(beginAt)
			if !flush || cost > time.Second {
				slog.Warn("sentry_flush", "flush", flush, "cost", cost.String())
			}
		}
	}()

	// Flush buffered events before the program terminates.
	// Set the timeout to the maximum duration the program can afford to wait.
	return sentry.Flush
}
