package mongos

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"net/url"
	"strings"
	"time"

	"git.7k7k.com/data/abScheduler/infra/config"
	"git.7k7k.com/pkg/common/metric"
	"github.com/qiniu/qmgo"
	qmgo_options "github.com/qiniu/qmgo/options"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/aws/aws-sdk-go-v2/aws"
	aconfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
)

func init() {
	log.SetFlags(log.LstdFlags | log.Llongfile)
}

type MongoSet struct {
	nameIdx map[string]int

	clients   []*qmgo.Client
	databases []*qmgo.Database
	metrics   []*metric.DatabaseRequests
	isAWS     []bool

	config map[string]config.Mongo
}

// @autowire(set=dao)
func NewMongos(conf *config.Config) *MongoSet {
	ms := &MongoSet{
		config:    conf.DataBase.MongoDB,
		nameIdx:   make(map[string]int, len(conf.DataBase.MongoDB)),
		clients:   make([]*qmgo.Client, len(conf.DataBase.MongoDB)),
		databases: make([]*qmgo.Database, len(conf.DataBase.MongoDB)),
		metrics:   make([]*metric.DatabaseRequests, len(conf.DataBase.MongoDB)),
		isAWS:     make([]bool, len(conf.DataBase.MongoDB)),
	}
	ms.InitDB(true)

	return ms
}

func (m *MongoSet) GetDB(name string) *qmgo.Database {
	idx := m.nameIdx[name]

	if m.isAWS[idx] {
		if err := m.clients[idx].Ping(1); err != nil && strings.Contains(err.Error(), "Authentication") {
			m.InitDB(false)
		}
	}

	return m.databases[idx]
}

func (m *MongoSet) Coll(db, coll string) *qmgo.Collection {
	return m.GetDB(db).Collection(coll)
}

func (m *MongoSet) CollFn(db, coll string) func() *qmgo.Collection {
	return func() *qmgo.Collection {
		return m.GetDB(db).Collection(coll)
	}
}

func (m *MongoSet) Metric(name string) *metric.DatabaseRequests {
	return m.metrics[m.nameIdx[name]]
}

func (m *MongoSet) InitDB(init bool) {
	for name, c := range m.config {
		if init {
			idx := len(m.nameIdx)
			m.nameIdx[name] = idx
			// m.metrics[idx] = metric.NewDatabaseRequests(name, extractHostPort(c.URI))
			m.metrics[idx] = metric.NewDatabaseRequests(name, "some_host")
			m.isAWS[idx] = c.Auth == "aws"
		}
		idx := m.nameIdx[name]

		if c.Auth == "aws" {
			s := m.LoadAWSConfig(&c)
			slog.Info("load_mongo_config", "config", c, "init", init, "secret", s)
		}

		ctx := context.Background()
		client, err := qmgo.NewClient(ctx, &qmgo.Config{Uri: c.URI}, qmgo_options.ClientOptions{
			ClientOptions: &options.ClientOptions{
				ConnectTimeout: lo.ToPtr(300 * time.Millisecond),
				MaxPoolSize:    lo.ToPtr(uint64(500)),
				// Timeout:        lo.ToPtr(300 * time.Millisecond),
			},
		})
		if err != nil {
			panic(err)
		}

		m.databases[idx] = client.Database(c.DB)
		if m.clients[idx] != nil { // 释放掉之前的client，避免内存泄露
			m.clients[idx].Close(ctx)
		}
		m.clients[idx] = client
	}
	return
}

// extractHostPort
// ****************************************************************************
func extractHostPort(dsn string) string {
	u, err := url.Parse(dsn)
	if err != nil {
		panic(err)
	}
	return u.Host
}

type AWSSecret struct {
	Username string `json:"username,omitempty"`
	Password string `json:"password,omitempty"`
}

func (m *MongoSet) LoadAWSConfig(c *config.Mongo) *secretsmanager.GetSecretValueOutput {
	region := "us-east-2"

	config, err := aconfig.LoadDefaultConfig(context.TODO(), aconfig.WithRegion(region))
	if err != nil {
		panic(err)
	}

	// Create Secrets Manager client
	svc := secretsmanager.NewFromConfig(config)

	input := &secretsmanager.GetSecretValueInput{
		SecretId:     aws.String(c.SecretName),
		VersionStage: aws.String("AWSCURRENT"), // VersionStage defaults to AWSCURRENT if unspecified
	}

	result, err := svc.GetSecretValue(context.TODO(), input)
	if err != nil {
		// For a list of exceptions thrown, see
		// https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
		panic(err)
	}

	log.Println("secret", *result.Name, *result.ARN, *result.CreatedDate, *result.SecretString, *result.VersionId)

	as := AWSSecret{}
	err = json.Unmarshal([]byte(*result.SecretString), &as)
	if err != nil {
		panic(err)
	}

	c.URI = fmt.Sprintf(c.URI, as.Username, url.QueryEscape(as.Password))
	return result
}
