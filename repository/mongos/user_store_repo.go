package mongos

import (
	"context"
	"log/slog"
	"math/rand/v2"
	"time"

	"git.7k7k.com/data/abScheduler/model"
	"git.7k7k.com/pkg/common/metric"
	"github.com/cockroachdb/errors"
	"github.com/qiniu/qmgo"
	"github.com/qiniu/qmgo/operator"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// UserStateRepo 处理用户状态在 MongoDB 中的存储和查询逻辑
type UserStateRepo struct {
	userStateColl func() *qmgo.Collection  // 用户状态集合
	mongoMtr      *metric.DatabaseRequests // MongoDB 请求指标
}

// NewUserStateRepo 创建 UserStoreRepo 的新实例
// @autowire(set=repository)
func NewUserStateRepo(mongoSet *MongoSet) *UserStateRepo {
	return &UserStateRepo{
		userStateColl: mongoSet.CollFn("abtest", "user_state"),
		mongoMtr:      mongoSet.Metric("abtest"),
	}
}

func (r *UserStateRepo) Coll() *qmgo.Collection {
	return r.userStateColl()
}

// UpsertUserStates 批量更新或插入用户状态到 MongoDB
func (r *UserStateRepo) UpsertUserStates(ctx context.Context, states []model.UserState) error {
	if len(states) == 0 {
		return nil
	}

	colNameState := r.userStateColl().GetCollectionName()
	models := []mongo.WriteModel{}

	for _, state := range states {
		models = append(models, mongo.NewUpdateOneModel().
			SetFilter(bson.M{"uid": state.UID, "utype": state.UType, "layer_id": state.LayerId}).
			SetUpdate(bson.M{
				operator.SetOnInsert: bson.M{
					"_id":        state.ObjectID,
					"uid":        state.UID,
					"utype":      state.UType,
					"layer_id":   state.LayerId,
					"created_at": state.CreatedAt,
				},
				operator.Set: bson.M{
					"exp_id":     state.ExpId,
					"grp_id":     state.GrpId,
					"rand":       state.Rand,
					"enter_at":   state.EnterAt,
					"expire_at":  state.ExpireAt,
					"updated_at": time.Now(),
				},
			}).
			SetUpsert(true))
	}

	coll, _ := r.userStateColl().CloneCollection()
	beginAt := time.Now()
	var result *mongo.BulkWriteResult
	_, err := lo.Attempt(3, func(_ int) error {
		var err error
		result, err = coll.BulkWrite(ctx, models, options.BulkWrite())
		if err != nil {
			time.Sleep(time.Second)
			return err
		}
		return nil
	})
	timeCost := time.Since(beginAt)
	r.mongoMtr.Emit(colNameState, "BulkUpsert", "0", timeCost)
	if err != nil {
		slog.ErrorContext(ctx, "flush_to_mongo_failed", "error", err.Error(), "result", result)
		return err
	}

	slog.InfoContext(ctx, "flush_suc", "result", result, "time_cost", timeCost.String())
	return nil
}

// DeleteUserStates 将用户状态标记为已过期
func (r *UserStateRepo) DeleteUserStates(ctx context.Context, states []model.UserState) error {
	if len(states) == 0 {
		return nil
	}

	colNameState := r.userStateColl().GetCollectionName()
	models := []mongo.WriteModel{}

	for _, state := range states {
		models = append(models, mongo.NewUpdateOneModel().
			SetFilter(bson.M{"uid": state.UID, "utype": state.UType, "layer_id": state.LayerId}).
			SetUpdate(bson.M{"$set": bson.M{"expire_at": state.ExpireAt}}))
	}

	coll, _ := r.userStateColl().CloneCollection()
	beginAt := time.Now()
	_, err := coll.BulkWrite(ctx, models, options.BulkWrite())
	r.mongoMtr.Emit(colNameState, "BulkDelete", "0", time.Since(beginAt))

	return err
}

// ListWithExp 获取参与指定实验的所有用户ID
func (r *UserStateRepo) ListWithExp(ctx context.Context, expID int, pageSize int64) (chan []string, error) {
	if pageSize <= 0 {
		pageSize = 500
	}

	uidsCh := make(chan []string, 16)

	go func() {
		defer close(uidsCh)

		cursor := primitive.NilObjectID
		total := int64(0)

		for {
			items := make([]model.UserState, 0, pageSize)

			filter := bson.M{"exp_id": expID}
			if !cursor.IsZero() {
				filter["_id"] = bson.M{"$gt": cursor}
			}

			beginAt := time.Now()
			err := r.userStateColl().Find(ctx, filter).Sort("_id").Limit(pageSize).All(&items)
			r.mongoMtr.Emit("user_state", "ListWithExp", "0", time.Since(beginAt))
			if err != nil {
				slog.ErrorContext(ctx, "ListWithExp failed", "error", err.Error())
				time.Sleep(time.Second)
				continue
			}

			total += int64(len(items))
			uidsCh <- lo.Map(items, func(item model.UserState, _ int) string { return item.UID })

			if len(items) < int(pageSize) {
				break
			}
			cursor = items[len(items)-1].ObjectID
		}

		slog.InfoContext(ctx, "ListWithExp", "total", total)
	}()

	return uidsCh, nil
}

// MoveUserStates 将用户从一个层移动到另一个层
func (r *UserStateRepo) MoveUserStates(ctx context.Context, uss []model.UserState, toLayerId, toGroupID, toExpID int) error {
	if len(uss) == 0 {
		return nil
	}

	oids := lo.Map(uss, func(us model.UserState, _ int) primitive.ObjectID { return us.ObjectID })
	_, err := r.userStateColl().UpdateAll(ctx,
		bson.M{"_id": bson.M{"$in": oids}},
		bson.M{"$set": bson.M{
			"layer_id": toLayerId,
			"grp_id":   toGroupID,
			"exp_id":   toExpID,
		}})

	if err != nil {
		return errors.Wrap(err, "update mongo failed")
	}

	return nil
}

// FetchAllExpIds 获取 MongoDB 中所有实验ID
func (r *UserStateRepo) FetchAllExpIds(ctx context.Context) ([]int, error) {
	var expIds []int
	cursor := 1 << 30

	for {
		coll := r.userStateColl()
		items := []model.UserState{}
		beginAt := time.Now()
		err := coll.Find(ctx, bson.M{"exp_id": bson.M{"$lt": cursor}}).Sort("-exp_id").Limit(1).All(&items)
		r.mongoMtr.Emit("user_state", "FetchAllExpIds", "0", time.Since(beginAt))
		if err != nil {
			return nil, errors.Wrap(err, "get_exp_ids_failed")
		}

		if len(items) == 0 {
			break
		}

		expIds = append(expIds, items[0].ExpId)
		cursor = items[0].ExpId
	}

	return expIds, nil
}

// CountGroupUserStates 统计指定实验组的用户数量
func (r *UserStateRepo) CountGroupUserStates(ctx context.Context, grpId int64) (int64, error) {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"grp_id": int32(grpId),
			},
		},
		{
			"$group": bson.M{
				"_id":   "$grp_id",
				"count": bson.M{"$sum": 1},
			},
		},
	}

	var result []bson.M
	err := r.userStateColl().Aggregate(ctx, pipeline).All(&result)
	if err != nil {
		return 0, errors.Wrap(err, "aggregate user state failed")
	}

	count := int64(0)
	if len(result) > 0 {
		if countVal, ok := result[0]["count"].(int32); ok {
			count = int64(countVal)
		}
	}

	return count, nil
}

// CountGroupUserStatesV2 统计指定实验组的用户数量
func (r *UserStateRepo) CountGroupUserStatesV2(ctx context.Context, grpId int64) (int64, error) {
	coll, err := r.userStateColl().CloneCollection()
	if err != nil {
		return 0, errors.Wrap(err, "aggregate user state failed")
	}

	return coll.CountDocuments(ctx, bson.M{"grp_id": grpId})
}

// CreateUserState 创建新的用户状态
func (r *UserStateRepo) CreateUserState(userID string, layerID, groupID, expID int, expireAt time.Time) model.UserState {
	return model.UserState{
		ObjectID:  primitive.NewObjectID(),
		UType:     model.UserTypeVisitor,
		UID:       userID,
		LayerId:   layerID,
		ExpId:     expID,
		GrpId:     groupID,
		EnterAt:   time.Now(),
		ExpireAt:  expireAt,
		Rand:      rand.Int(),
		CreatedAt: time.Now(),
	}
}
