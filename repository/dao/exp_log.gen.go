// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newExpLog(db *gorm.DB, opts ...gen.DOOption) expLog {
	_expLog := expLog{}

	_expLog.expLogDo.UseDB(db, opts...)
	_expLog.expLogDo.UseModel(&model.ExpLog{})

	tableName := _expLog.expLogDo.TableName()
	_expLog.ALL = field.NewAsterisk(tableName)
	_expLog.ID = field.NewInt64(tableName, "id")
	_expLog.ExpID = field.NewInt64(tableName, "exp_id")
	_expLog.ExpSnapshot = field.NewString(tableName, "exp_snapshot")
	_expLog.Changes = field.NewString(tableName, "changes")
	_expLog.Desc = field.NewString(tableName, "desc")
	_expLog.UID = field.NewString(tableName, "uid")
	_expLog.Uname = field.NewString(tableName, "uname")
	_expLog.CreateTime = field.NewTime(tableName, "create_time")

	_expLog.fillFieldMap()

	return _expLog
}

type expLog struct {
	expLogDo

	ALL         field.Asterisk
	ID          field.Int64
	ExpID       field.Int64
	ExpSnapshot field.String
	Changes     field.String
	Desc        field.String
	UID         field.String
	Uname       field.String
	CreateTime  field.Time

	fieldMap map[string]field.Expr
}

func (e expLog) Table(newTableName string) *expLog {
	e.expLogDo.UseTable(newTableName)
	return e.updateTableName(newTableName)
}

func (e expLog) As(alias string) *expLog {
	e.expLogDo.DO = *(e.expLogDo.As(alias).(*gen.DO))
	return e.updateTableName(alias)
}

func (e *expLog) updateTableName(table string) *expLog {
	e.ALL = field.NewAsterisk(table)
	e.ID = field.NewInt64(table, "id")
	e.ExpID = field.NewInt64(table, "exp_id")
	e.ExpSnapshot = field.NewString(table, "exp_snapshot")
	e.Changes = field.NewString(table, "changes")
	e.Desc = field.NewString(table, "desc")
	e.UID = field.NewString(table, "uid")
	e.Uname = field.NewString(table, "uname")
	e.CreateTime = field.NewTime(table, "create_time")

	e.fillFieldMap()

	return e
}

func (e *expLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := e.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (e *expLog) fillFieldMap() {
	e.fieldMap = make(map[string]field.Expr, 8)
	e.fieldMap["id"] = e.ID
	e.fieldMap["exp_id"] = e.ExpID
	e.fieldMap["exp_snapshot"] = e.ExpSnapshot
	e.fieldMap["changes"] = e.Changes
	e.fieldMap["desc"] = e.Desc
	e.fieldMap["uid"] = e.UID
	e.fieldMap["uname"] = e.Uname
	e.fieldMap["create_time"] = e.CreateTime
}

func (e expLog) clone(db *gorm.DB) expLog {
	e.expLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return e
}

func (e expLog) replaceDB(db *gorm.DB) expLog {
	e.expLogDo.ReplaceDB(db)
	return e
}

type expLogDo struct{ gen.DO }

type IExpLogDo interface {
	gen.SubQuery
	Debug() IExpLogDo
	WithContext(ctx context.Context) IExpLogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IExpLogDo
	WriteDB() IExpLogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IExpLogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IExpLogDo
	Not(conds ...gen.Condition) IExpLogDo
	Or(conds ...gen.Condition) IExpLogDo
	Select(conds ...field.Expr) IExpLogDo
	Where(conds ...gen.Condition) IExpLogDo
	Order(conds ...field.Expr) IExpLogDo
	Distinct(cols ...field.Expr) IExpLogDo
	Omit(cols ...field.Expr) IExpLogDo
	Join(table schema.Tabler, on ...field.Expr) IExpLogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IExpLogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IExpLogDo
	Group(cols ...field.Expr) IExpLogDo
	Having(conds ...gen.Condition) IExpLogDo
	Limit(limit int) IExpLogDo
	Offset(offset int) IExpLogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IExpLogDo
	Unscoped() IExpLogDo
	Create(values ...*model.ExpLog) error
	CreateInBatches(values []*model.ExpLog, batchSize int) error
	Save(values ...*model.ExpLog) error
	First() (*model.ExpLog, error)
	Take() (*model.ExpLog, error)
	Last() (*model.ExpLog, error)
	Find() ([]*model.ExpLog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ExpLog, err error)
	FindInBatches(result *[]*model.ExpLog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ExpLog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IExpLogDo
	Assign(attrs ...field.AssignExpr) IExpLogDo
	Joins(fields ...field.RelationField) IExpLogDo
	Preload(fields ...field.RelationField) IExpLogDo
	FirstOrInit() (*model.ExpLog, error)
	FirstOrCreate() (*model.ExpLog, error)
	FindByPage(offset int, limit int) (result []*model.ExpLog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IExpLogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (e expLogDo) Debug() IExpLogDo {
	return e.withDO(e.DO.Debug())
}

func (e expLogDo) WithContext(ctx context.Context) IExpLogDo {
	return e.withDO(e.DO.WithContext(ctx))
}

func (e expLogDo) ReadDB() IExpLogDo {
	return e.Clauses(dbresolver.Read)
}

func (e expLogDo) WriteDB() IExpLogDo {
	return e.Clauses(dbresolver.Write)
}

func (e expLogDo) Session(config *gorm.Session) IExpLogDo {
	return e.withDO(e.DO.Session(config))
}

func (e expLogDo) Clauses(conds ...clause.Expression) IExpLogDo {
	return e.withDO(e.DO.Clauses(conds...))
}

func (e expLogDo) Returning(value interface{}, columns ...string) IExpLogDo {
	return e.withDO(e.DO.Returning(value, columns...))
}

func (e expLogDo) Not(conds ...gen.Condition) IExpLogDo {
	return e.withDO(e.DO.Not(conds...))
}

func (e expLogDo) Or(conds ...gen.Condition) IExpLogDo {
	return e.withDO(e.DO.Or(conds...))
}

func (e expLogDo) Select(conds ...field.Expr) IExpLogDo {
	return e.withDO(e.DO.Select(conds...))
}

func (e expLogDo) Where(conds ...gen.Condition) IExpLogDo {
	return e.withDO(e.DO.Where(conds...))
}

func (e expLogDo) Order(conds ...field.Expr) IExpLogDo {
	return e.withDO(e.DO.Order(conds...))
}

func (e expLogDo) Distinct(cols ...field.Expr) IExpLogDo {
	return e.withDO(e.DO.Distinct(cols...))
}

func (e expLogDo) Omit(cols ...field.Expr) IExpLogDo {
	return e.withDO(e.DO.Omit(cols...))
}

func (e expLogDo) Join(table schema.Tabler, on ...field.Expr) IExpLogDo {
	return e.withDO(e.DO.Join(table, on...))
}

func (e expLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IExpLogDo {
	return e.withDO(e.DO.LeftJoin(table, on...))
}

func (e expLogDo) RightJoin(table schema.Tabler, on ...field.Expr) IExpLogDo {
	return e.withDO(e.DO.RightJoin(table, on...))
}

func (e expLogDo) Group(cols ...field.Expr) IExpLogDo {
	return e.withDO(e.DO.Group(cols...))
}

func (e expLogDo) Having(conds ...gen.Condition) IExpLogDo {
	return e.withDO(e.DO.Having(conds...))
}

func (e expLogDo) Limit(limit int) IExpLogDo {
	return e.withDO(e.DO.Limit(limit))
}

func (e expLogDo) Offset(offset int) IExpLogDo {
	return e.withDO(e.DO.Offset(offset))
}

func (e expLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IExpLogDo {
	return e.withDO(e.DO.Scopes(funcs...))
}

func (e expLogDo) Unscoped() IExpLogDo {
	return e.withDO(e.DO.Unscoped())
}

func (e expLogDo) Create(values ...*model.ExpLog) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Create(values)
}

func (e expLogDo) CreateInBatches(values []*model.ExpLog, batchSize int) error {
	return e.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (e expLogDo) Save(values ...*model.ExpLog) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Save(values)
}

func (e expLogDo) First() (*model.ExpLog, error) {
	if result, err := e.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ExpLog), nil
	}
}

func (e expLogDo) Take() (*model.ExpLog, error) {
	if result, err := e.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ExpLog), nil
	}
}

func (e expLogDo) Last() (*model.ExpLog, error) {
	if result, err := e.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ExpLog), nil
	}
}

func (e expLogDo) Find() ([]*model.ExpLog, error) {
	result, err := e.DO.Find()
	return result.([]*model.ExpLog), err
}

func (e expLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ExpLog, err error) {
	buf := make([]*model.ExpLog, 0, batchSize)
	err = e.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (e expLogDo) FindInBatches(result *[]*model.ExpLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return e.DO.FindInBatches(result, batchSize, fc)
}

func (e expLogDo) Attrs(attrs ...field.AssignExpr) IExpLogDo {
	return e.withDO(e.DO.Attrs(attrs...))
}

func (e expLogDo) Assign(attrs ...field.AssignExpr) IExpLogDo {
	return e.withDO(e.DO.Assign(attrs...))
}

func (e expLogDo) Joins(fields ...field.RelationField) IExpLogDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Joins(_f))
	}
	return &e
}

func (e expLogDo) Preload(fields ...field.RelationField) IExpLogDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Preload(_f))
	}
	return &e
}

func (e expLogDo) FirstOrInit() (*model.ExpLog, error) {
	if result, err := e.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ExpLog), nil
	}
}

func (e expLogDo) FirstOrCreate() (*model.ExpLog, error) {
	if result, err := e.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ExpLog), nil
	}
}

func (e expLogDo) FindByPage(offset int, limit int) (result []*model.ExpLog, count int64, err error) {
	result, err = e.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = e.Offset(-1).Limit(-1).Count()
	return
}

func (e expLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = e.Count()
	if err != nil {
		return
	}

	err = e.Offset(offset).Limit(limit).Scan(result)
	return
}

func (e expLogDo) Scan(result interface{}) (err error) {
	return e.DO.Scan(result)
}

func (e expLogDo) Delete(models ...*model.ExpLog) (result gen.ResultInfo, err error) {
	return e.DO.Delete(models)
}

func (e *expLogDo) withDO(do gen.Dao) *expLogDo {
	e.DO = *do.(*gen.DO)
	return e
}
