// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newExp(db *gorm.DB, opts ...gen.DOOption) exp {
	_exp := exp{}

	_exp.expDo.UseDB(db, opts...)
	_exp.expDo.UseModel(&model.Exp{})

	tableName := _exp.expDo.TableName()
	_exp.ALL = field.NewAsterisk(tableName)
	_exp.ID = field.NewInt64(tableName, "id")
	_exp.ProjectID = field.NewInt64(tableName, "project_id")
	_exp.LayerID = field.NewInt64(tableName, "layer_id")
	_exp.Name = field.NewString(tableName, "name")
	_exp.LayerRate = field.NewInt32(tableName, "layer_rate")
	_exp.ExpType = field.NewInt32(tableName, "exp_type")
	_exp.TagID = field.NewString(tableName, "tag_id")
	_exp.BatchID = field.NewInt64(tableName, "batch_id")
	_exp.Days = field.NewInt32(tableName, "days")
	_exp.Strategy = field.NewString(tableName, "strategy")
	_exp.Csr = field.NewString(tableName, "csr")
	_exp.UID = field.NewInt64(tableName, "uid")
	_exp.Owner = field.NewString(tableName, "owner")
	_exp.Desc = field.NewString(tableName, "desc")
	_exp.Step = field.NewInt32(tableName, "step")
	_exp.State = field.NewInt32(tableName, "state")
	_exp.IsDeleted = field.NewInt32(tableName, "is_deleted")
	_exp.StartTime = field.NewTime(tableName, "start_time")
	_exp.EndTime = field.NewTime(tableName, "end_time")
	_exp.Version = field.NewInt64(tableName, "version")
	_exp.CreateTime = field.NewTime(tableName, "create_time")
	_exp.UpdateTime = field.NewTime(tableName, "update_time")

	_exp.fillFieldMap()

	return _exp
}

type exp struct {
	expDo

	ALL        field.Asterisk
	ID         field.Int64
	ProjectID  field.Int64
	LayerID    field.Int64
	Name       field.String
	LayerRate  field.Int32
	ExpType    field.Int32
	TagID      field.String
	BatchID    field.Int64
	Days       field.Int32
	Strategy   field.String
	Csr        field.String
	UID        field.Int64
	Owner      field.String
	Desc       field.String
	Step       field.Int32
	State      field.Int32
	IsDeleted  field.Int32
	StartTime  field.Time
	EndTime    field.Time
	Version    field.Int64
	CreateTime field.Time
	UpdateTime field.Time

	fieldMap map[string]field.Expr
}

func (e exp) Table(newTableName string) *exp {
	e.expDo.UseTable(newTableName)
	return e.updateTableName(newTableName)
}

func (e exp) As(alias string) *exp {
	e.expDo.DO = *(e.expDo.As(alias).(*gen.DO))
	return e.updateTableName(alias)
}

func (e *exp) updateTableName(table string) *exp {
	e.ALL = field.NewAsterisk(table)
	e.ID = field.NewInt64(table, "id")
	e.ProjectID = field.NewInt64(table, "project_id")
	e.LayerID = field.NewInt64(table, "layer_id")
	e.Name = field.NewString(table, "name")
	e.LayerRate = field.NewInt32(table, "layer_rate")
	e.ExpType = field.NewInt32(table, "exp_type")
	e.TagID = field.NewString(table, "tag_id")
	e.BatchID = field.NewInt64(table, "batch_id")
	e.Days = field.NewInt32(table, "days")
	e.Strategy = field.NewString(table, "strategy")
	e.Csr = field.NewString(table, "csr")
	e.UID = field.NewInt64(table, "uid")
	e.Owner = field.NewString(table, "owner")
	e.Desc = field.NewString(table, "desc")
	e.Step = field.NewInt32(table, "step")
	e.State = field.NewInt32(table, "state")
	e.IsDeleted = field.NewInt32(table, "is_deleted")
	e.StartTime = field.NewTime(table, "start_time")
	e.EndTime = field.NewTime(table, "end_time")
	e.Version = field.NewInt64(table, "version")
	e.CreateTime = field.NewTime(table, "create_time")
	e.UpdateTime = field.NewTime(table, "update_time")

	e.fillFieldMap()

	return e
}

func (e *exp) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := e.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (e *exp) fillFieldMap() {
	e.fieldMap = make(map[string]field.Expr, 22)
	e.fieldMap["id"] = e.ID
	e.fieldMap["project_id"] = e.ProjectID
	e.fieldMap["layer_id"] = e.LayerID
	e.fieldMap["name"] = e.Name
	e.fieldMap["layer_rate"] = e.LayerRate
	e.fieldMap["exp_type"] = e.ExpType
	e.fieldMap["tag_id"] = e.TagID
	e.fieldMap["batch_id"] = e.BatchID
	e.fieldMap["days"] = e.Days
	e.fieldMap["strategy"] = e.Strategy
	e.fieldMap["csr"] = e.Csr
	e.fieldMap["uid"] = e.UID
	e.fieldMap["owner"] = e.Owner
	e.fieldMap["desc"] = e.Desc
	e.fieldMap["step"] = e.Step
	e.fieldMap["state"] = e.State
	e.fieldMap["is_deleted"] = e.IsDeleted
	e.fieldMap["start_time"] = e.StartTime
	e.fieldMap["end_time"] = e.EndTime
	e.fieldMap["version"] = e.Version
	e.fieldMap["create_time"] = e.CreateTime
	e.fieldMap["update_time"] = e.UpdateTime
}

func (e exp) clone(db *gorm.DB) exp {
	e.expDo.ReplaceConnPool(db.Statement.ConnPool)
	return e
}

func (e exp) replaceDB(db *gorm.DB) exp {
	e.expDo.ReplaceDB(db)
	return e
}

type expDo struct{ gen.DO }

type IExpDo interface {
	gen.SubQuery
	Debug() IExpDo
	WithContext(ctx context.Context) IExpDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IExpDo
	WriteDB() IExpDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IExpDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IExpDo
	Not(conds ...gen.Condition) IExpDo
	Or(conds ...gen.Condition) IExpDo
	Select(conds ...field.Expr) IExpDo
	Where(conds ...gen.Condition) IExpDo
	Order(conds ...field.Expr) IExpDo
	Distinct(cols ...field.Expr) IExpDo
	Omit(cols ...field.Expr) IExpDo
	Join(table schema.Tabler, on ...field.Expr) IExpDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IExpDo
	RightJoin(table schema.Tabler, on ...field.Expr) IExpDo
	Group(cols ...field.Expr) IExpDo
	Having(conds ...gen.Condition) IExpDo
	Limit(limit int) IExpDo
	Offset(offset int) IExpDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IExpDo
	Unscoped() IExpDo
	Create(values ...*model.Exp) error
	CreateInBatches(values []*model.Exp, batchSize int) error
	Save(values ...*model.Exp) error
	First() (*model.Exp, error)
	Take() (*model.Exp, error)
	Last() (*model.Exp, error)
	Find() ([]*model.Exp, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Exp, err error)
	FindInBatches(result *[]*model.Exp, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Exp) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IExpDo
	Assign(attrs ...field.AssignExpr) IExpDo
	Joins(fields ...field.RelationField) IExpDo
	Preload(fields ...field.RelationField) IExpDo
	FirstOrInit() (*model.Exp, error)
	FirstOrCreate() (*model.Exp, error)
	FindByPage(offset int, limit int) (result []*model.Exp, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IExpDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (e expDo) Debug() IExpDo {
	return e.withDO(e.DO.Debug())
}

func (e expDo) WithContext(ctx context.Context) IExpDo {
	return e.withDO(e.DO.WithContext(ctx))
}

func (e expDo) ReadDB() IExpDo {
	return e.Clauses(dbresolver.Read)
}

func (e expDo) WriteDB() IExpDo {
	return e.Clauses(dbresolver.Write)
}

func (e expDo) Session(config *gorm.Session) IExpDo {
	return e.withDO(e.DO.Session(config))
}

func (e expDo) Clauses(conds ...clause.Expression) IExpDo {
	return e.withDO(e.DO.Clauses(conds...))
}

func (e expDo) Returning(value interface{}, columns ...string) IExpDo {
	return e.withDO(e.DO.Returning(value, columns...))
}

func (e expDo) Not(conds ...gen.Condition) IExpDo {
	return e.withDO(e.DO.Not(conds...))
}

func (e expDo) Or(conds ...gen.Condition) IExpDo {
	return e.withDO(e.DO.Or(conds...))
}

func (e expDo) Select(conds ...field.Expr) IExpDo {
	return e.withDO(e.DO.Select(conds...))
}

func (e expDo) Where(conds ...gen.Condition) IExpDo {
	return e.withDO(e.DO.Where(conds...))
}

func (e expDo) Order(conds ...field.Expr) IExpDo {
	return e.withDO(e.DO.Order(conds...))
}

func (e expDo) Distinct(cols ...field.Expr) IExpDo {
	return e.withDO(e.DO.Distinct(cols...))
}

func (e expDo) Omit(cols ...field.Expr) IExpDo {
	return e.withDO(e.DO.Omit(cols...))
}

func (e expDo) Join(table schema.Tabler, on ...field.Expr) IExpDo {
	return e.withDO(e.DO.Join(table, on...))
}

func (e expDo) LeftJoin(table schema.Tabler, on ...field.Expr) IExpDo {
	return e.withDO(e.DO.LeftJoin(table, on...))
}

func (e expDo) RightJoin(table schema.Tabler, on ...field.Expr) IExpDo {
	return e.withDO(e.DO.RightJoin(table, on...))
}

func (e expDo) Group(cols ...field.Expr) IExpDo {
	return e.withDO(e.DO.Group(cols...))
}

func (e expDo) Having(conds ...gen.Condition) IExpDo {
	return e.withDO(e.DO.Having(conds...))
}

func (e expDo) Limit(limit int) IExpDo {
	return e.withDO(e.DO.Limit(limit))
}

func (e expDo) Offset(offset int) IExpDo {
	return e.withDO(e.DO.Offset(offset))
}

func (e expDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IExpDo {
	return e.withDO(e.DO.Scopes(funcs...))
}

func (e expDo) Unscoped() IExpDo {
	return e.withDO(e.DO.Unscoped())
}

func (e expDo) Create(values ...*model.Exp) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Create(values)
}

func (e expDo) CreateInBatches(values []*model.Exp, batchSize int) error {
	return e.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (e expDo) Save(values ...*model.Exp) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Save(values)
}

func (e expDo) First() (*model.Exp, error) {
	if result, err := e.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Exp), nil
	}
}

func (e expDo) Take() (*model.Exp, error) {
	if result, err := e.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Exp), nil
	}
}

func (e expDo) Last() (*model.Exp, error) {
	if result, err := e.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Exp), nil
	}
}

func (e expDo) Find() ([]*model.Exp, error) {
	result, err := e.DO.Find()
	return result.([]*model.Exp), err
}

func (e expDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Exp, err error) {
	buf := make([]*model.Exp, 0, batchSize)
	err = e.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (e expDo) FindInBatches(result *[]*model.Exp, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return e.DO.FindInBatches(result, batchSize, fc)
}

func (e expDo) Attrs(attrs ...field.AssignExpr) IExpDo {
	return e.withDO(e.DO.Attrs(attrs...))
}

func (e expDo) Assign(attrs ...field.AssignExpr) IExpDo {
	return e.withDO(e.DO.Assign(attrs...))
}

func (e expDo) Joins(fields ...field.RelationField) IExpDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Joins(_f))
	}
	return &e
}

func (e expDo) Preload(fields ...field.RelationField) IExpDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Preload(_f))
	}
	return &e
}

func (e expDo) FirstOrInit() (*model.Exp, error) {
	if result, err := e.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Exp), nil
	}
}

func (e expDo) FirstOrCreate() (*model.Exp, error) {
	if result, err := e.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Exp), nil
	}
}

func (e expDo) FindByPage(offset int, limit int) (result []*model.Exp, count int64, err error) {
	result, err = e.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = e.Offset(-1).Limit(-1).Count()
	return
}

func (e expDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = e.Count()
	if err != nil {
		return
	}

	err = e.Offset(offset).Limit(limit).Scan(result)
	return
}

func (e expDo) Scan(result interface{}) (err error) {
	return e.DO.Scan(result)
}

func (e expDo) Delete(models ...*model.Exp) (result gen.ResultInfo, err error) {
	return e.DO.Delete(models)
}

func (e *expDo) withDO(do gen.Dao) *expDo {
	e.DO = *do.(*gen.DO)
	return e
}
