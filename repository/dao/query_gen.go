// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:         db,
		Exp:        newExp(db, opts...),
		ExpLog:     newExpLog(db, opts...),
		Group:      newGroup(db, opts...),
		Layer:      newLayer(db, opts...),
		Project:    newProject(db, opts...),
		SplitGroup: newSplitGroup(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Exp        exp
	ExpLog     expLog
	Group      group
	Layer      layer
	Project    project
	SplitGroup splitGroup
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:         db,
		Exp:        q.Exp.clone(db),
		ExpLog:     q.ExpLog.clone(db),
		Group:      q.Group.clone(db),
		Layer:      q.Layer.clone(db),
		Project:    q.Project.clone(db),
		SplitGroup: q.SplitGroup.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:         db,
		Exp:        q.Exp.replaceDB(db),
		ExpLog:     q.ExpLog.replaceDB(db),
		Group:      q.Group.replaceDB(db),
		Layer:      q.Layer.replaceDB(db),
		Project:    q.Project.replaceDB(db),
		SplitGroup: q.SplitGroup.replaceDB(db),
	}
}

type queryCtx struct {
	Exp        IExpDo
	ExpLog     IExpLogDo
	Group      IGroupDo
	Layer      ILayerDo
	Project    IProjectDo
	SplitGroup ISplitGroupDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Exp:        q.Exp.WithContext(ctx),
		ExpLog:     q.ExpLog.WithContext(ctx),
		Group:      q.Group.WithContext(ctx),
		Layer:      q.Layer.WithContext(ctx),
		Project:    q.Project.WithContext(ctx),
		SplitGroup: q.SplitGroup.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
