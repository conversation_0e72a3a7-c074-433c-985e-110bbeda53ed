// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newSplitGroup(db *gorm.DB, opts ...gen.DOOption) splitGroup {
	_splitGroup := splitGroup{}

	_splitGroup.splitGroupDo.UseDB(db, opts...)
	_splitGroup.splitGroupDo.UseModel(&model.SplitGroup{})

	tableName := _splitGroup.splitGroupDo.TableName()
	_splitGroup.ALL = field.NewAsterisk(tableName)
	_splitGroup.ID = field.NewInt64(tableName, "id")
	_splitGroup.FormExpID = field.NewInt64(tableName, "form_exp_id")
	_splitGroup.FromExpName = field.NewString(tableName, "from_exp_name")
	_splitGroup.FromGroupID = field.NewInt64(tableName, "from_group_id")
	_splitGroup.FromGroupName = field.NewString(tableName, "from_group_name")
	_splitGroup.ToExpID = field.NewInt64(tableName, "to_exp_id")
	_splitGroup.ToExpName = field.NewString(tableName, "to_exp_name")
	_splitGroup.ToGroupID = field.NewInt64(tableName, "to_group_id")
	_splitGroup.ToGroupName = field.NewString(tableName, "to_group_name")
	_splitGroup.Rate = field.NewInt32(tableName, "rate")
	_splitGroup.SplitType = field.NewInt32(tableName, "split_type")
	_splitGroup.Desc = field.NewString(tableName, "desc")
	_splitGroup.State = field.NewInt32(tableName, "state")
	_splitGroup.CreateTime = field.NewTime(tableName, "create_time")
	_splitGroup.UpdateTime = field.NewTime(tableName, "update_time")

	_splitGroup.fillFieldMap()

	return _splitGroup
}

type splitGroup struct {
	splitGroupDo

	ALL           field.Asterisk
	ID            field.Int64
	FormExpID     field.Int64
	FromExpName   field.String
	FromGroupID   field.Int64
	FromGroupName field.String
	ToExpID       field.Int64
	ToExpName     field.String
	ToGroupID     field.Int64
	ToGroupName   field.String
	Rate          field.Int32
	SplitType     field.Int32
	Desc          field.String
	State         field.Int32
	CreateTime    field.Time
	UpdateTime    field.Time

	fieldMap map[string]field.Expr
}

func (s splitGroup) Table(newTableName string) *splitGroup {
	s.splitGroupDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s splitGroup) As(alias string) *splitGroup {
	s.splitGroupDo.DO = *(s.splitGroupDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *splitGroup) updateTableName(table string) *splitGroup {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.FormExpID = field.NewInt64(table, "form_exp_id")
	s.FromExpName = field.NewString(table, "from_exp_name")
	s.FromGroupID = field.NewInt64(table, "from_group_id")
	s.FromGroupName = field.NewString(table, "from_group_name")
	s.ToExpID = field.NewInt64(table, "to_exp_id")
	s.ToExpName = field.NewString(table, "to_exp_name")
	s.ToGroupID = field.NewInt64(table, "to_group_id")
	s.ToGroupName = field.NewString(table, "to_group_name")
	s.Rate = field.NewInt32(table, "rate")
	s.SplitType = field.NewInt32(table, "split_type")
	s.Desc = field.NewString(table, "desc")
	s.State = field.NewInt32(table, "state")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")

	s.fillFieldMap()

	return s
}

func (s *splitGroup) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *splitGroup) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 15)
	s.fieldMap["id"] = s.ID
	s.fieldMap["form_exp_id"] = s.FormExpID
	s.fieldMap["from_exp_name"] = s.FromExpName
	s.fieldMap["from_group_id"] = s.FromGroupID
	s.fieldMap["from_group_name"] = s.FromGroupName
	s.fieldMap["to_exp_id"] = s.ToExpID
	s.fieldMap["to_exp_name"] = s.ToExpName
	s.fieldMap["to_group_id"] = s.ToGroupID
	s.fieldMap["to_group_name"] = s.ToGroupName
	s.fieldMap["rate"] = s.Rate
	s.fieldMap["split_type"] = s.SplitType
	s.fieldMap["desc"] = s.Desc
	s.fieldMap["state"] = s.State
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
}

func (s splitGroup) clone(db *gorm.DB) splitGroup {
	s.splitGroupDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s splitGroup) replaceDB(db *gorm.DB) splitGroup {
	s.splitGroupDo.ReplaceDB(db)
	return s
}

type splitGroupDo struct{ gen.DO }

type ISplitGroupDo interface {
	gen.SubQuery
	Debug() ISplitGroupDo
	WithContext(ctx context.Context) ISplitGroupDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISplitGroupDo
	WriteDB() ISplitGroupDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISplitGroupDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISplitGroupDo
	Not(conds ...gen.Condition) ISplitGroupDo
	Or(conds ...gen.Condition) ISplitGroupDo
	Select(conds ...field.Expr) ISplitGroupDo
	Where(conds ...gen.Condition) ISplitGroupDo
	Order(conds ...field.Expr) ISplitGroupDo
	Distinct(cols ...field.Expr) ISplitGroupDo
	Omit(cols ...field.Expr) ISplitGroupDo
	Join(table schema.Tabler, on ...field.Expr) ISplitGroupDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISplitGroupDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISplitGroupDo
	Group(cols ...field.Expr) ISplitGroupDo
	Having(conds ...gen.Condition) ISplitGroupDo
	Limit(limit int) ISplitGroupDo
	Offset(offset int) ISplitGroupDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISplitGroupDo
	Unscoped() ISplitGroupDo
	Create(values ...*model.SplitGroup) error
	CreateInBatches(values []*model.SplitGroup, batchSize int) error
	Save(values ...*model.SplitGroup) error
	First() (*model.SplitGroup, error)
	Take() (*model.SplitGroup, error)
	Last() (*model.SplitGroup, error)
	Find() ([]*model.SplitGroup, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SplitGroup, err error)
	FindInBatches(result *[]*model.SplitGroup, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SplitGroup) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISplitGroupDo
	Assign(attrs ...field.AssignExpr) ISplitGroupDo
	Joins(fields ...field.RelationField) ISplitGroupDo
	Preload(fields ...field.RelationField) ISplitGroupDo
	FirstOrInit() (*model.SplitGroup, error)
	FirstOrCreate() (*model.SplitGroup, error)
	FindByPage(offset int, limit int) (result []*model.SplitGroup, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISplitGroupDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s splitGroupDo) Debug() ISplitGroupDo {
	return s.withDO(s.DO.Debug())
}

func (s splitGroupDo) WithContext(ctx context.Context) ISplitGroupDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s splitGroupDo) ReadDB() ISplitGroupDo {
	return s.Clauses(dbresolver.Read)
}

func (s splitGroupDo) WriteDB() ISplitGroupDo {
	return s.Clauses(dbresolver.Write)
}

func (s splitGroupDo) Session(config *gorm.Session) ISplitGroupDo {
	return s.withDO(s.DO.Session(config))
}

func (s splitGroupDo) Clauses(conds ...clause.Expression) ISplitGroupDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s splitGroupDo) Returning(value interface{}, columns ...string) ISplitGroupDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s splitGroupDo) Not(conds ...gen.Condition) ISplitGroupDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s splitGroupDo) Or(conds ...gen.Condition) ISplitGroupDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s splitGroupDo) Select(conds ...field.Expr) ISplitGroupDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s splitGroupDo) Where(conds ...gen.Condition) ISplitGroupDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s splitGroupDo) Order(conds ...field.Expr) ISplitGroupDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s splitGroupDo) Distinct(cols ...field.Expr) ISplitGroupDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s splitGroupDo) Omit(cols ...field.Expr) ISplitGroupDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s splitGroupDo) Join(table schema.Tabler, on ...field.Expr) ISplitGroupDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s splitGroupDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISplitGroupDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s splitGroupDo) RightJoin(table schema.Tabler, on ...field.Expr) ISplitGroupDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s splitGroupDo) Group(cols ...field.Expr) ISplitGroupDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s splitGroupDo) Having(conds ...gen.Condition) ISplitGroupDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s splitGroupDo) Limit(limit int) ISplitGroupDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s splitGroupDo) Offset(offset int) ISplitGroupDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s splitGroupDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISplitGroupDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s splitGroupDo) Unscoped() ISplitGroupDo {
	return s.withDO(s.DO.Unscoped())
}

func (s splitGroupDo) Create(values ...*model.SplitGroup) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s splitGroupDo) CreateInBatches(values []*model.SplitGroup, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s splitGroupDo) Save(values ...*model.SplitGroup) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s splitGroupDo) First() (*model.SplitGroup, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SplitGroup), nil
	}
}

func (s splitGroupDo) Take() (*model.SplitGroup, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SplitGroup), nil
	}
}

func (s splitGroupDo) Last() (*model.SplitGroup, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SplitGroup), nil
	}
}

func (s splitGroupDo) Find() ([]*model.SplitGroup, error) {
	result, err := s.DO.Find()
	return result.([]*model.SplitGroup), err
}

func (s splitGroupDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SplitGroup, err error) {
	buf := make([]*model.SplitGroup, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s splitGroupDo) FindInBatches(result *[]*model.SplitGroup, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s splitGroupDo) Attrs(attrs ...field.AssignExpr) ISplitGroupDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s splitGroupDo) Assign(attrs ...field.AssignExpr) ISplitGroupDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s splitGroupDo) Joins(fields ...field.RelationField) ISplitGroupDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s splitGroupDo) Preload(fields ...field.RelationField) ISplitGroupDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s splitGroupDo) FirstOrInit() (*model.SplitGroup, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SplitGroup), nil
	}
}

func (s splitGroupDo) FirstOrCreate() (*model.SplitGroup, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SplitGroup), nil
	}
}

func (s splitGroupDo) FindByPage(offset int, limit int) (result []*model.SplitGroup, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s splitGroupDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s splitGroupDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s splitGroupDo) Delete(models ...*model.SplitGroup) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *splitGroupDo) withDO(do gen.Dao) *splitGroupDo {
	s.DO = *do.(*gen.DO)
	return s
}
