package cache

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"strconv"
	"sync/atomic"
	"time"

	"git.7k7k.com/pkg/common/redisutils"
	"git.7k7k.com/pkg/storage/codec"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
)

// Redis前缀常量，从UserStateService移植
const RedisPrefixUserState = "uste"

// HitState 存储用户在某一层的命中状态
type HitState struct {
	ExpID        int    `json:"exp_id,omitempty"`     // 实验ID
	GroupID      int    `json:"group_id,omitempty"`   // 方案ID
	EnterAt      int    `json:"enter_at,omitempty"`   // 进组时间。unix时间戳
	ExpiredAt    int    `json:"expired_at,omitempty"` // 过期时间。unix时间戳
	CsrIndentity string `json:"csr,omitzero"`
}

// PrjCache 存储用户在所有层的命中状态
type PrjCache map[int]*HitState // layerID -> *HitState

// UserStateCache 用户状态缓存服务，负责处理用户实验状态的缓存操作
type UserStateCache struct {
	redis redis.UniversalClient
}

// NewUserStateCache 创建用户状态缓存服务
func NewUserStateCache(redis redis.UniversalClient) *UserStateCache {
	return &UserStateCache{redis: redis}
}

// 以下是HitState的方法

// Exit 用户退出实验
func (s *HitState) Exit() {
	s.ExpID = 0
	s.GroupID = 0
}

// IsExpired 判断用户状态是否过期
func (s *HitState) IsExpired() bool {
	return s.ExpiredAt < int(time.Now().Unix())
}

// Enter 用户进入实验
func (s *HitState) Enter(expID, grpId int, expire time.Duration) {
	s.ExpID = expID
	s.GroupID = grpId
	s.EnterAt = int(time.Now().Unix())
	if expire <= 0 {
		expire = time.Hour * 24 * 365
	}
	s.ExpiredAt = int(time.Now().Add(expire).Unix())
}

// 以下是LayersHitState的方法

// DeepClone 深拷贝用户状态
func (ls PrjCache) DeepClone() PrjCache {
	new := PrjCache{}
	for k, vPtr := range ls {
		v := *vPtr
		new[k] = &v
	}
	return new
}

// 以下是UserStateCache的方法

// BuildCacheKey 构建用户状态的Redis key
func (c *UserStateCache) BuildCacheKey(prjID int, userID string) string {
	return fmt.Sprintf("%s:%d:%s", RedisPrefixUserState, prjID, userID)
}

// GetOne 获取单个层缓存
func (c *UserStateCache) GetOne(ctx context.Context, prjID int, userID string, layerID int) (hitState *HitState, err error) {
	key := c.BuildCacheKey(prjID, userID)

	kv, err := c.redis.HGet(ctx, key, strconv.Itoa(layerID)).Result()
	if err != nil {
		return nil, err
	}

	hitState = &HitState{}
	err = codec.Default.UnmarshalFromString(kv, hitState)
	if err != nil {
		return nil, err
	}
	return hitState, nil
}

// Gets 获取用户状态，一个项目下的所有层缓存
func (c *UserStateCache) Gets(ctx context.Context, prjID int, userID string) (layersState PrjCache, err error) {
	key := c.BuildCacheKey(prjID, userID)

	kv, err := c.redis.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	layersState = make(PrjCache)
	for k, v := range kv {
		layerID, _ := strconv.Atoi(k)
		ls := &HitState{}
		err = codec.Default.UnmarshalFromString(v, ls)
		if err != nil {
			return nil, err
		}
		layersState[layerID] = ls
	}

	return layersState, nil
}

// SetLayers 设置用户状态的多个层
func (c *UserStateCache) SetLayers(ctx context.Context, prjID int, userID string, layersState PrjCache) error {
	if len(layersState) == 0 {
		return nil
	}

	key := c.BuildCacheKey(prjID, userID)
	kvs := lo.FlatMap(lo.Entries(layersState), func(entry lo.Entry[int, *HitState], _ int) []any {
		v, _ := codec.Default.MarshalToString(entry.Value)
		return []any{entry.Key, v}
	})

	_, err := c.redis.HSet(ctx, key, kvs...).Result()
	if err != nil {
		return err
	}

	_, err = c.redis.Expire(ctx, key, time.Hour*24*7).Result()
	return err
}

// RemoveLayers 按层删除用户状态
func (c *UserStateCache) RemoveLayers(ctx context.Context, prjID int, userID string, layerIDs ...int) error {
	if len(layerIDs) == 0 {
		return nil
	}

	key := c.BuildCacheKey(prjID, userID)
	fields := make([]string, 0, len(layerIDs))
	for _, layerID := range layerIDs {
		fields = append(fields, strconv.Itoa(layerID))
	}

	_, err := c.redis.HDel(ctx, key, fields...).Result()
	return err
}

// RemoveAll 删除用户的所有状态
func (c *UserStateCache) RemoveAll(ctx context.Context, prjID int, userID string) (int64, error) {
	key := c.BuildCacheKey(prjID, userID)
	return c.redis.Del(ctx, key).Result()
}

// CleanProject 清理项目下的所有缓存，仅用来清理压测数据
func (c *UserStateCache) CleanProject(ctx context.Context, prjId int) {
	cnt := int64(0)
	err := redisutils.ScanRedis(c.redis, fmt.Sprintf("%s:%d:*", RedisPrefixUserState, prjId), func(keys []string) error {
		pipe := c.redis.Pipeline()
		for _, key := range keys {
			pipe.Del(ctx, key)
		}
		v, err := pipe.Exec(ctx)
		if err != nil {
			slog.ErrorContext(ctx, "Del failed", "error", err)
			return err
		}
		atomic.AddInt64(&cnt, int64(len(v)))
		log.Println(cnt)
		return nil
	}, &redisutils.ScanOpt{
		Concurrent: 10,
		PageSize:   100,
	})
	if err != nil {
		slog.ErrorContext(ctx, "ScanRedis failed", "error", err)
	}
}
