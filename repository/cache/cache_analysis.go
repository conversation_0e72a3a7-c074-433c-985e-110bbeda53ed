package cache

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/redis/go-redis/v9"
)

// ProjectMemoryStats 项目内存使用统计
type ProjectMemoryStats struct {
	ProjectID   int     // 项目ID
	KeyCount    int64   // key数量
	TotalBytes  int64   // 总字节数
	AvgKeyBytes float64 // 平均每个key占用字节数
}

// AnalysisAllProjects 分析所有项目的Redis内存使用情况
// 只扫描一轮key，同时获取内存占用，最多扫描指定数量的key
func AnalysisAllProjects(ctx context.Context, cli redis.UniversalClient, maxKeys int64) (map[int]*ProjectMemoryStats, error) {
	// 用于存储每个项目的统计信息
	projectStats := make(map[int]*ProjectMemoryStats)

	// 使用SCAN命令遍历所有以"uste:"开头的key
	var cursor uint64
	pattern := fmt.Sprintf("%s:*", RedisPrefixUserState)
	var totalScanned int64 = 0

	for totalScanned < maxKeys {
		// 扫描一批key
		var scanResult *redis.ScanCmd
		scanResult = cli.Scan(ctx, cursor, pattern, 1000)
		keys, nextCursor, err := scanResult.Result()
		if err != nil {
			return nil, fmt.Errorf("scan keys failed: %w", err)
		}

		if len(keys) == 0 {
			if nextCursor == 0 {
				break // 已扫描完所有key
			}
			cursor = nextCursor
			continue
		}

		// 使用pipeline批量获取这批key的内存占用
		pipe := cli.Pipeline()
		memoryCommands := make([]*redis.IntCmd, len(keys))
		for i, key := range keys {
			memoryCommands[i] = pipe.MemoryUsage(ctx, key)
		}

		_, err = pipe.Exec(ctx)
		if err != nil {
			return nil, fmt.Errorf("get memory usage failed: %w", err)
		}

		// 处理每个key的结果
		for i, key := range keys {
			// 解析项目ID
			parts := strings.Split(key, ":")
			if len(parts) < 3 {
				continue // 跳过格式不正确的key
			}

			prjID, err := strconv.Atoi(parts[1])
			if err != nil {
				continue // 跳过无效的项目ID
			}

			// 获取key的内存占用
			bytes, err := memoryCommands[i].Result()
			if err != nil {
				continue // 忽略获取内存失败的key
			}

			// 获取或创建项目统计信息
			stats, exists := projectStats[prjID]
			if !exists {
				stats = &ProjectMemoryStats{
					ProjectID: prjID,
				}
				projectStats[prjID] = stats
			}

			// 更新统计信息
			stats.KeyCount++
			stats.TotalBytes += bytes
		}

		// 更新总扫描数量
		totalScanned += int64(len(keys))

		// 更新cursor
		cursor = nextCursor

		// 如果cursor为0，说明已遍历完所有key
		if cursor == 0 {
			break
		}
	}

	// 计算每个项目的平均值
	for _, stats := range projectStats {
		if stats.KeyCount > 0 {
			stats.AvgKeyBytes = float64(stats.TotalBytes) / float64(stats.KeyCount)
		}
	}

	return projectStats, nil
}
