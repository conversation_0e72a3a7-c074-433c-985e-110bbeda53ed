package repository

import (
	"context"
	"regexp"
	"time"

	"git.7k7k.com/data/abScheduler/infra/config"
	"git.7k7k.com/data/abScheduler/repository/dao"
	"git.7k7k.com/pkg/common/gosentry"
	"git.7k7k.com/pkg/common/logs"
	"git.7k7k.com/pkg/common/metric"
	"github.com/samber/lo"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/callbacks"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/dbresolver"
)

var dbs map[string]*gorm.DB = make(map[string]*gorm.DB, 4)

// 新增一个公共函数，用于初始化数据库连接，接受数据库类型作为参数
func newDB(dbname string, c *config.Config) (gormdb *gorm.DB) {
	var err error
	userConfig := c.DataBase.MySQL[dbname]
	master := userConfig.Write
	if master == nil {
		master = lo.ToPtr(userConfig)
	}

	config := &gorm.Config{}

	gormdb, err = gorm.Open(mysql.Open(master.DSN), config)
	if err != nil {
		panic("无法连接到数据库: " + err.Error())
	}

	slave := userConfig.Read
	if slave != nil { // 读写分离
		gormdb.Use(dbresolver.Register(dbresolver.Config{
			Replicas: []gorm.Dialector{mysql.Open(slave.DSN)},
		}))
	}

	gormdb.Use(&gosentry.GormPlugin{})
	if master.Debug {
		gormdb.Use(&logs.GormPlugin{})
	}

	// addr := extractHostPort(c.DataBase.MySQL[dbname].DSN)

	// gormdb.Callback().Query().Before("gorm:query").Register("metrics:query_before", MetricCallbackBefore(dbname, addr))
	// gormdb.Callback().Create().Before("gorm:create").Register("metrics:create_before", MetricCallbackBefore(dbname, addr))
	// gormdb.Callback().Update().Before("gorm:update").Register("metrics:update_before", MetricCallbackBefore(dbname, addr))
	// gormdb.Callback().Delete().Before("gorm:delete").Register("metrics:delete_before", MetricCallbackBefore(dbname, addr))

	// gormdb.Callback().Query().After("gorm:query").Register("metrics:query_after", MetricCallbackAfter(dbname, addr))
	// gormdb.Callback().Create().After("gorm:create").Register("metrics:create_after", MetricCallbackAfter(dbname, addr))
	// gormdb.Callback().Update().After("gorm:update").Register("metrics:update_after", MetricCallbackAfter(dbname, addr))
	// gormdb.Callback().Delete().After("gorm:delete").Register("metrics:delete_after", MetricCallbackAfter(dbname, addr))

	return gormdb
}

func initOnce(dbname string, c *config.Config) *gorm.DB {
	if _, ok := dbs[dbname]; !ok {
		// TODO 加锁
		dbs[dbname] = newDB(dbname, c)
	}

	return dbs[dbname]
}

// @autowire(set=dao)
func InitDefaultDB(c *config.Config) *gorm.DB { return initOnce(c.DataBase.MySQLDefault, c) }

// @autowire(set=dao)
func InitDefaultQuery(db *gorm.DB) *dao.Query {
	return dao.Use(db)
}

type QueryAdmin dao.Query

// @autowire(set=dao)
func InitAdminQuery(c *config.Config) *QueryAdmin {
	return (*QueryAdmin)(dao.Use(initOnce("admindb", c)))
}

func MetricCallbackBefore(name string, addr string) func(db *gorm.DB) {
	return func(db *gorm.DB) {
		beginAt, ok := db.Statement.Context.Value("tsm").(map[string]time.Time)
		if !ok {
			beginAt = make(map[string]time.Time)
			db.Statement.Context = context.WithValue(db.Statement.Context, "tsm", beginAt)
		}
		callbacks.BuildQuerySQL(db)
		sql := db.Statement.SQL.String()
		beginAt[sql] = time.Now()
		// dump.Dump(beginAt[sql], sql)
	}
}

func MetricCallbackAfter(name string, addr string) func(db *gorm.DB) {
	dm := metric.NewDatabaseRequests(name, addr)
	return func(db *gorm.DB) {
		sql := db.Statement.SQL.String()
		beginAt := db.Statement.Context.Value("tsm").(map[string]time.Time)
		// dump.Dump(beginAt[sql], sql)
		ts := time.Since(beginAt[sql])

		table := db.Statement.Table
		method := db.Statement.BuildClauses[0]
		dm.Emit(table, method, "0", ts)
		// dump.Dump(ts)
	}
}

func LoggerAppendTrace(logger logger.Interface) logger.Interface {
	return &GormLoggerPlus{Interface: logger}
}

type GormLoggerPlus struct {
	logger.Interface
}

func (l *GormLoggerPlus) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	// sql, _ := fc()
	l.Interface.Trace(ctx, begin, fc, err)
}

var hostPortRegex = regexp.MustCompile(`@tcp\(([^\s]+):(\d+)\)`)

// dsn = xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(*************:23306)/abtest_dev?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s
func extractHostPort(dsn string) string {
	matches := hostPortRegex.FindStringSubmatch(dsn)
	if len(matches) != 3 {
		panic("无法从DSN中提取主机和端口")
	}
	return matches[1] + ":" + matches[2]
}
