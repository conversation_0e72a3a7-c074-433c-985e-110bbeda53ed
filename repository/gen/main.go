package gen

import (
	admin "git.7k7k.com/data/abAdmin/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

var models = []any{
	admin.Project{},
	admin.Layer{},
	admin.Exp{},
	admin.Group{},
	admin.SplitGroup{},
	admin.ExpLog{},
}

func Gen() {
	gen_default()
}

func gen_default() {
	g := gen.NewGenerator(gen.Config{
		OutPath: "./repository/dao",
		OutFile: "query_gen.go",
		Mode:    gen.WithoutContext | gen.WithQueryInterface, // generate mode
		// Mode:    gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // generate mode
	})

	dsn := "xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(106.75.29.167:23306)/abtest_dev?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
	gormdb, _ := gorm.Open(mysql.Open(dsn))
	g.UseDB(gormdb)

	g.ApplyBasic(models...)
	g.ApplyInterface(func(BaseQuery) {}, models...)

	g.Execute()
}
