package mysql

import (
	"context"
	"log/slog"
	"time"

	"git.7k7k.com/data/abScheduler/model"
	"github.com/cockroachdb/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// UserStateRepo 处理用户状态在 MySQL 中的存储和查询逻辑
type UserStateRepo struct {
	db *gorm.DB // MySQL 数据库连接
}

// NewUserStateRepo 创建 UserStateRepo 的新实例
// @autowire(set=repository)
func NewUserStateRepo(db *gorm.DB) *UserStateRepo {
	return &UserStateRepo{
		db: db,
	}
}

// UpsertUserStates 批量更新或插入用户状态到 MySQL
func (r *UserStateRepo) UpsertUserStates(ctx context.Context, states []model.UserState) error {
	if len(states) == 0 {
		return nil
	}

	_, err := lo.Attempt(3, func(index int) error {
		return r.db.WithContext(ctx).Table("user_state").
			Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "uid"}, {Name: "layer_id"}},
				DoUpdates: clause.AssignmentColumns([]string{"exp_id", "grp_id", "enter_at", "expire_at"}),
			}).
			Create(&states).Error
	})
	if err != nil {
		slog.ErrorContext(ctx, "flush_to_mysql_failed", "error", err.Error())
		return errors.Wrap(err, "upsert user states failed")
	}

	return nil
}

// DeleteUserStates 将用户状态标记为已过期
func (r *UserStateRepo) DeleteUserStates(ctx context.Context, states []model.UserState) error {
	if len(states) == 0 {
		return nil
	}

	filters := [][]any{}
	for _, state := range states {
		filters = append(filters, []any{state.UID, state.LayerId})
	}

	err := r.db.WithContext(ctx).Table("user_state").
		Where("(uid,layer_id) in ?", filters).
		Update("expire_at", time.Now()).Error

	if err != nil {
		slog.ErrorContext(ctx, "flush_to_mysql_failed", "error", err.Error())
		return errors.Wrap(err, "delete user states failed")
	}

	return nil
}

// MoveUserStates 将用户从一个层移动到另一个层
func (r *UserStateRepo) MoveUserStates(ctx context.Context, ids []int, toLayerId, toGroupID, toExpID int) error {
	if len(ids) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).Table("user_state").
		Where("id in ?", ids).
		Updates(map[string]any{
			"layer_id":   toLayerId,
			"grp_id":     toGroupID,
			"exp_id":     toExpID,
			"updated_at": time.Now(),
		}).Error

	if err != nil {
		return errors.Wrap(err, "move user states failed")
	}

	return nil
}

// CleanExpiredUserStates 清理过期的用户状态
func (r *UserStateRepo) CleanExpiredUserStates(ctx context.Context, batchSize int) (int64, error) {
	if batchSize <= 0 {
		batchSize = 200
	}

	result := r.db.WithContext(ctx).
		Table("user_state").
		Where("expire_at < ? and expire_at > ?", time.Now(), time.Unix(0, 0)).
		Order("expire_at").
		Limit(batchSize).
		Updates(map[string]interface{}{
			"exp_id":     0,
			"grp_id":     0,
			"expire_at":  time.Unix(0, 0),
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		return 0, errors.Wrap(result.Error, "clean expired user states failed")
	}

	return result.RowsAffected, nil
}

// GetUserStates 根据用户ID和层ID获取用户状态
func (r *UserStateRepo) GetUserStates(ctx context.Context, uid string, layerIDs []int) ([]model.UserState, error) {
	var states []model.UserState

	err := r.db.WithContext(ctx).
		Where("uid = ? AND layer_id IN ?", uid, layerIDs).
		Find(&states).Error

	if err != nil {
		return nil, errors.Wrap(err, "get user states failed")
	}

	return states, nil
}

// CreateUserState 创建新的用户状态记录
func (r *UserStateRepo) CreateUserState(userID string, layerID, groupID, expID int, expireAt time.Time) model.UserState {
	return model.UserState{
		UType:     model.UserTypeVisitor,
		UID:       userID,
		LayerId:   layerID,
		GrpId:     groupID,
		ExpId:     expID,
		EnterAt:   time.Now(),
		ExpireAt:  expireAt,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// FetchAllExpIds 获取 MySQL 中所有不同的实验ID
// 使用 DISTINCT 查询来获取所有唯一的实验ID，并按降序排序
func (r *UserStateRepo) FetchAllExpIds(ctx context.Context) ([]int, error) {
	var expIds []int

	err := r.db.WithContext(ctx).
		Table("user_state").
		Distinct().
		Where("exp_id > 0"). // 排除无效的实验ID
		Order("exp_id DESC").
		Pluck("exp_id", &expIds).Error

	if err != nil {
		return nil, errors.Wrap(err, "get_exp_ids_failed")
	}

	return expIds, nil
}
