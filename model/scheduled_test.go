package model

import (
	"reflect"
	"testing"
)

func TestGroup_CalcRedirect(t *testing.T) {
	sg1 := &SplitGroup{Rate: 100}
	tests := []struct {
		name string
		g    *Group
		want *SplitGroup
	}{
		{
			name: "empty redirect",
			g: &Group{
				RedirectTo: []*SplitGroup{},
			},
			want: nil,
		},
		{
			name: "single redirect",
			g: &Group{
				RedirectTo: []*SplitGroup{sg1},
			},
			want: sg1,
		},
		{
			name: "multiple redirects",
			g: &Group{
				RedirectTo: []*SplitGroup{
					{Rate: 3000},
					{Rate: 2000},
					{Rate: 1000},
				},
			},
			// want will be checked in the test
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.name == "empty redirect" || tt.name == "single redirect" {
				got := tt.g.CalcRedirectForUser("")
				if !reflect.DeepEqual(got, tt.want) {
					t.<PERSON>("CalcRedirect() = %v, want %v", got, tt.want)
				}
				return
			}

			// 对于多重定向测试，我们只检查分布
			counts := make(map[*SplitGroup]int)
			iterations := 10000

			for i := 0; i < iterations; i++ {
				// 使用不同的用户ID来获取更好的分布
				uid := generateUID(i)
				result := tt.g.CalcRedirectForUser(uid)
				if result != nil {
					counts[result]++
				}
			}

			// Check distribution
			tolerance := 0.1 // 允许10%的误差
			for _, sg := range tt.g.RedirectTo {
				expectedRate := float64(sg.Rate) / 10000.0
				actualRate := float64(counts[sg]) / float64(iterations)
				if abs(expectedRate-actualRate) > tolerance {
					t.Errorf("Rate for group with weight %d: expected around %.2f, got %.2f",
						sg.Rate, expectedRate, actualRate)
				}
			}
		})
	}
}

func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

func TestProjectIdx_PickDomain(t *testing.T) {
	tests := []struct {
		name    string
		project *ProjectIdx
	}{
		{
			name: "单域测试",
			project: &ProjectIdx{
				Domains: []*Domain{
					{ModRight: 1000, LayerIDs: []LayerID{1}},
				},
			},
		},
		{
			name: "多域均匀分布测试",
			project: &ProjectIdx{
				Domains: []*Domain{
					{ModRight: 250, LayerIDs: []LayerID{1}},
					{ModRight: 500, LayerIDs: []LayerID{2}},
					{ModRight: 750, LayerIDs: []LayerID{3}},
					{ModRight: 1000, LayerIDs: []LayerID{4}},
				},
			},
		},
		{
			name: "多域不均匀分布测试",
			project: &ProjectIdx{
				Domains: []*Domain{
					{ModRight: 100, LayerIDs: []LayerID{1}},  // 10%
					{ModRight: 300, LayerIDs: []LayerID{2}},  // 20%
					{ModRight: 600, LayerIDs: []LayerID{3}},  // 30%
					{ModRight: 1000, LayerIDs: []LayerID{4}}, // 40%
				},
			},
		},
		{
			name: "极端不均匀分布测试",
			project: &ProjectIdx{
				Domains: []*Domain{
					{ModRight: 1, LayerIDs: []LayerID{1}},    // 0.1%
					{ModRight: 10, LayerIDs: []LayerID{2}},   // 0.9%
					{ModRight: 100, LayerIDs: []LayerID{3}},  // 9%
					{ModRight: 999, LayerIDs: []LayerID{4}},  // 89.9%
					{ModRight: 1000, LayerIDs: []LayerID{5}}, // 0.1%
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 统计每个域的命中次数
			domainCounts := make(map[int]int)
			iterations := 100000 // 增加到10万次迭代

			// 生成不同的用户ID并测试分布
			for i := 0; i < iterations; i++ {
				uid := generateUID(i)
				domain := tt.project.PickDomain(uid)
				if domain == nil {
					t.Fatalf("PickDomain返回nil，用户ID: %s", uid)
				}

				// 找到这个域在数组中的索引
				for idx, d := range tt.project.Domains {
					if d == domain {
						domainCounts[idx]++
						break
					}
				}
			}

			// 验证分布是否在预期范围内
			tolerance := 0.01 // 降低到1%的误差
			prevModRight := 0

			t.Logf("域分布详情 (总迭代次数: %d):", iterations)
			for idx, domain := range tt.project.Domains {
				expectedRate := float64(domain.ModRight-int32(prevModRight)) / float64(DomainMod)
				actualRate := float64(domainCounts[idx]) / float64(iterations)
				difference := abs(expectedRate - actualRate)

				t.Logf("域 %d (ModRight: %d): 期望比例 %.4f, 实际比例 %.4f, 差异 %.4f, 命中次数 %d",
					idx, domain.ModRight, expectedRate, actualRate, difference, domainCounts[idx])

				if difference > tolerance {
					t.Errorf("域 %d 的分布超出允许误差: 期望约 %.4f, 实际得到 %.4f, 差异 %.4f",
						idx, expectedRate, actualRate, difference)
				}

				prevModRight = int(domain.ModRight)
			}
		})
	}
}

// 生成不同的用户ID
func generateUID(i int) string {
	return "user_" + string(rune(i%26+65)) + "_" + string(rune(i/26%26+65)) + "_" + string(rune(i/676%26+65))
}
