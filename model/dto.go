package model

import (
	"strconv"

	"github.com/spf13/cast"
)

// ABLog 只记录分流成功的
type ABLog struct {
	Version    int            `json:"version"` // 3.0 统一写 3，区分2.0写的数据
	ProjectKey string         `json:"project_key"`
	DistinctID string         `json:"distinct_id"`
	ExpID      int            `json:"exp_id,omitzero"`
	GroupID    int            `json:"group_id,omitzero"`
	GroupName  string         `json:"group_name,omitzero"`
	LDebug     *LDebug        `json:"debug,omitzero"`
	Attr       map[string]any `json:"attr,omitzero"`
	Ts         int64          `json:"ts"`
	BundleID   string         `json:"bundle_id,omitzero"`
	BucketID   int64          `json:"bucket_id,omitzero"`    // 2.0字段
	UserWayNum string         `json:"user_way_num,omitzero"` // 2.0字段
}

type Debug struct {
	DomainID int             `json:"domain_id,omitzero"`
	Layers   map[int]*LDebug `json:"layers,omitempty"`
}

type LDebug struct {
	HitWhite      bool `json:"hit_white,omitempty"`      // 命中白名单
	HitCache      bool `json:"hit_cache,omitempty"`      // 命中缓存
	LeaveCache    int  `json:"leave_cache,omitempty"`    // 退出缓存，值为实验组ID
	MissBucket    bool `json:"miss_bucket,omitempty"`    // 未命中桶
	TriggerFailed bool `json:"trigger_failed,omitempty"` // 触发条件未命中
	Redirect      bool `json:"redirect,omitempty"`       // 通过重定向进组
	ParamInvalid  bool `json:"param_invalid,omitempty"`  // 参数非法
	Conflict      bool `json:"conflict,omitempty"`       // 跟更高优先级的方案冲突，特性有裁剪
	RateLimit     bool `json:"rate_limit,omitempty"`     // 限流
}

func (l *LDebug) HitCache01() string {
	return strconv.Itoa(cast.ToInt(l.HitCache))
}
