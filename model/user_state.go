package model

import (
	"context"
	"math/rand/v2"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"gorm.io/gorm"
)

const (
	UserTypeVisitor = 1 // distinct_id、访客ID
)

// UserState
// 唯一键：layer_id, utype, uid
type UserState struct {
	ID        int                `gorm:"column:id" bson:"id" json:"id"`
	ObjectID  primitive.ObjectID `gorm:"-" bson:"_id" json:"Id"`
	UType     int                `gorm:"column:utype" bson:"utype,omitempty"` // 用户ID类型
	UID       string             `gorm:"column:uid" bson:"uid,omitempty"`
	LayerId   int                `gorm:"column:layer_id" bson:"layer_id,omitempty"`
	ExpId     int                `gorm:"column:exp_id" bson:"exp_id,omitempty"`
	GrpId     int                `gorm:"column:grp_id" bson:"grp_id,omitempty"`
	EnterAt   time.Time          `gorm:"column:enter_at" bson:"enter_at,omitempty"`
	ExpireAt  time.Time          `gorm:"column:expire_at" bson:"expire_at,omitempty"`
	Rand      int                `gorm:"-" bson:"rand,omitempty"` // 随机数，用来重定向时随机排序
	CreatedAt time.Time          `gorm:"column:created_at" bson:"created_at"`
	UpdatedAt time.Time          `gorm:"column:updated_at" bson:"updated_at"`
}

func (s *UserState) TableName() string {
	return "user_state"
}

func (s *UserState) BeforeSave(tx *gorm.DB) error {
	return nil
}

func (s *UserState) BeforeInsert(ctx context.Context) error {
	if s.ObjectID.IsZero() {
		s.ObjectID = primitive.NewObjectID()
	}
	if s.Rand == 0 {
		s.Rand = rand.Int()
	}
	return nil
}

func (s UserState) PartitionKey() []byte {
	return []byte(s.UID)
}

func (s *UserState) IsExpired() bool {
	return !s.ExpireAt.IsZero() && s.ExpireAt.Before(time.Now())
}
