package redises

import (
	"context"
	"log"
	"sync" // Added
	"time"

	"github.com/redis/go-redis/v9"
)

// ProgressUpdate stores the progress of a benchmark test for a client
type ProgressUpdate struct {
	ClientName    string
	ProcessedKeys int // Number of keys successfully processed (HGetAll returned data)
	TotalKeys     int // Total number of keys to process for this client
	ElapsedTime   time.Duration
	IsDone        bool
}

// testClientPerformance performs HGetAll on keys for a given client and sends progress updates.
// It sends an initial update, updates for each processed key, and a final update when done.
func testClientPerformance(
	ctx context.Context,
	client redis.UniversalClient,
	clientName string,
	keys []string,
	progressChan chan<- ProgressUpdate, // Send-only channel for progress
	wg *sync.WaitGroup,
) {
	defer wg.Done()           // Signal WaitGroup upon completion
	defer close(progressChan) // Close the progress channel when this goroutine exits

	if len(keys) == 0 {
		// If there are no keys, send a single "done" message immediately.
		progressChan <- ProgressUpdate{
			ClientName:    clientName,
			ProcessedKeys: 0,
			TotalKeys:     0,
			ElapsedTime:   0,
			IsDone:        true,
		}
		return
	}

	startTime := time.Now()
	processedOkCount := 0 // Counter for keys that were successfully processed and yielded data
	totalKeysToProcess := len(keys)

	// Send an initial progress update (0 keys processed)
	// This helps the periodic reporter show "0/TotalKeys" from the beginning.
	progressChan <- ProgressUpdate{
		ClientName:    clientName,
		ProcessedKeys: 0,
		TotalKeys:     totalKeysToProcess,
		ElapsedTime:   0, // Elapsed time is minimal at the start
		IsDone:        false,
	}

	for i, key := range keys {
		m, err := client.HGetAll(ctx, key).Result()
		// Count as processed if HGetAll succeeds and returns a non-empty map,
		// matching the original benchmark's success criteria.
		if err == nil && len(m) > 0 {
			processedOkCount++
		}

		currentElapsedTime := time.Since(startTime)
		isFinalUpdate := (i == totalKeysToProcess-1) // True if this is the last key

		// Send progress update for the current key
		progressChan <- ProgressUpdate{
			ClientName:    clientName,
			ProcessedKeys: processedOkCount,
			TotalKeys:     totalKeysToProcess,
			ElapsedTime:   currentElapsedTime,
			IsDone:        isFinalUpdate, // Mark as done only on the very last update
		}
	}
	// The loop ensures the last update sent has IsDone = true.
}

// RunBenchmark 测试 r1 和 r2 的性能。
// 它首先从 r1 中扫描指定前缀和数量的 key。
// 然后，它并发地使用 r1 和 r2 两个客户端对这些扫描到的 key 执行 HGetAll 操作，以测试它们的性能。
// 主协程会每秒钟打印一次当前的测试进度。
// 所有测试完成后，会打印详细的最终性能报告。
//
// 参数：
//
//	r1 - redis.UniversalClient: 用于扫描 key 的原始 Redis 客户端，同时它本身也会作为被测试性能的客户端之一。
//	r2 - redis.UniversalClient: 另一个待测试性能的 Redis 客户端。
//
// 性能指标包括：
//   - 目标扫描的 key 数量及实际扫描到的 key 数量。
//   - 每个客户端成功处理的 key 数量。
//   - 每个客户端处理这些 key 所花费的总时间。
//   - 每个客户端每秒处理的 key 数量（速率）。
//   - 整体基准测试的持续时间（包括扫描和并发测试）。
func RunBenchmark(r1, r2 redis.UniversalClient) {
	ctx := context.Background()
	var cursor uint64
	var N int = 200000 // 目标扫描的 key 数量
	var size int = 200 // 每次 SCAN 命令获取的 key 数量
	keys := make([]string, 0, N)

	log.Printf("Scanning keys from R1 (target: %d, page size: %d)...\n", N, size)
	scanStartTime := time.Now() // Record start time for the entire benchmark, including scan

	// 使用 SCAN 命令遍历符合条件的 key
	for {
		k, nextCursor, err := r2.Scan(ctx, cursor, "uste:*", int64(size)).Result()
		if err != nil {
			log.Printf("Error scanning keys: %v. Aborting benchmark.", err)
			panic(err) // 保持原始行为，扫描出错则 panic
		}
		keys = append(keys, k...)
		cursor = nextCursor
		// 如果 SCAN 返回的游标为0（表示遍历完成）或已收集到足够数量的 key，则停止
		if nextCursor == 0 || len(keys) >= N {
			break
		}
	}
	log.Printf("Finished scanning. Found %d keys in %s.\n", len(keys), time.Since(scanStartTime))

	// 如果实际扫描到的 key 数量超过目标 N，则截断到 N
	if len(keys) > N {
		keys = keys[:N]
		log.Printf("Truncated scanned keys to %d.\n", N)
	}

	// 如果没有扫描到任何 key，则无法进行测试，直接返回
	if len(keys) == 0 {
		log.Println("No keys found matching 'uste:*'. Benchmark cannot run.")
		return
	}

	var wg sync.WaitGroup // WaitGroup 用于等待所有测试协程完成
	// 创建无缓冲 channel 用于从测试协程接收进度更新
	progressR1Chan := make(chan ProgressUpdate)
	progressR2Chan := make(chan ProgressUpdate)

	// 初始化用于存储最新进度的变量
	latestProgressR1 := ProgressUpdate{ClientName: "R1", TotalKeys: len(keys)}
	latestProgressR2 := ProgressUpdate{ClientName: "R2", TotalKeys: len(keys)}
	r1Done := false // 标记 R1 测试是否完成
	r2Done := false // 标记 R2 测试是否完成

	wg.Add(2) // 设置 WaitGroup 需要等待2个协程
	log.Println("Starting benchmark for R1 and R2 concurrently...")
	// 启动两个协程分别测试 R1 和 R2
	go testClientPerformance(ctx, r1, "R1", keys, progressR1Chan, &wg)
	go testClientPerformance(ctx, r2, "R2", keys, progressR2Chan, &wg)

	ticker := time.NewTicker(1 * time.Second) // 创建1秒钟的定时器用于定期打印进度
	defer ticker.Stop()                       // 确保定时器停止

	benchmarkTasksStartTime := time.Now() // 记录并发测试任务开始的时间

	// 主循环，用于接收进度更新和处理定时器事件，直到两个测试都完成
	for !r1Done || !r2Done {
		select {
		case update, ok := <-progressR1Chan: // 从 R1 的进度 channel接收更新
			if !ok { // 如果 channel 关闭
				r1Done = true // 标记 R1 完成
				continue      // 继续 select，避免处理无效的 update
			}
			latestProgressR1 = update // 更新 R1 的最新进度
			if update.IsDone {
				r1Done = true // 如果更新标记为完成，则设置 R1 完成
			}
		case update, ok := <-progressR2Chan: // 从 R2 的进度 channel接收更新
			if !ok { // 如果 channel 关闭
				r2Done = true // 标记 R2 完成
				continue
			}
			latestProgressR2 = update // 更新 R2 的最新进度
			if update.IsDone {
				r2Done = true // 如果更新标记为完成，则设置 R2 完成
			}
		case <-ticker.C: // 定时器触发
			log.Println("--- Current Benchmark Progress ---")
			// 打印 R1 当前进度
			rateR1 := 0.0
			if latestProgressR1.ElapsedTime.Seconds() > 0 {
				rateR1 = float64(latestProgressR1.ProcessedKeys) / latestProgressR1.ElapsedTime.Seconds()
			}
			log.Printf("R1 - Processed: %d/%d keys, CurrentCost: %s, CurrentRate: %.2f keys/s (Done: %t)\n",
				latestProgressR1.ProcessedKeys, latestProgressR1.TotalKeys, latestProgressR1.ElapsedTime, rateR1, r1Done)

			// 打印 R2 当前进度
			rateR2 := 0.0
			if latestProgressR2.ElapsedTime.Seconds() > 0 {
				rateR2 = float64(latestProgressR2.ProcessedKeys) / latestProgressR2.ElapsedTime.Seconds()
			}
			log.Printf("R2 - Processed: %d/%d keys, CurrentCost: %s, CurrentRate: %.2f keys/s (Done: %t)\n",
				latestProgressR2.ProcessedKeys, latestProgressR2.TotalKeys, latestProgressR2.ElapsedTime, rateR2, r2Done)
			log.Printf("Benchmark tasks running time: %s\n", time.Since(benchmarkTasksStartTime))
			log.Println("----------------------------------")
		}
	}

	log.Println("Both R1 and R2 tests reported completion. Waiting for goroutines to fully exit...")
	wg.Wait() // 等待所有测试协程（testClientPerformance）执行完毕
	log.Println("All benchmark goroutines finished.")

	// 打印最终的基准测试结果
	log.Printf("--- Final Benchmark Results ---\n")
	log.Printf("Target Keys for Scan: %d\n", N)
	log.Printf("Actual Scanned Keys (and tested): %d\n", len(keys))

	// R1 最终结果
	// latestProgressR1 和 latestProgressR2 此时应持有 IsDone=true 的最终状态
	finalRateR1 := 0.0
	if latestProgressR1.ElapsedTime.Seconds() > 0 {
		finalRateR1 = float64(latestProgressR1.ProcessedKeys) / latestProgressR1.ElapsedTime.Seconds()
	}
	log.Printf("R1 - Processed: %d keys, TotalCost: %s, FinalRate: %.2f keys/s\n",
		latestProgressR1.ProcessedKeys, latestProgressR1.ElapsedTime, finalRateR1)

	// R2 最终结果
	finalRateR2 := 0.0
	if latestProgressR2.ElapsedTime.Seconds() > 0 {
		finalRateR2 = float64(latestProgressR2.ProcessedKeys) / latestProgressR2.ElapsedTime.Seconds()
	}
	log.Printf("R2 - Processed: %d keys, TotalCost: %s, FinalRate: %.2f keys/s\n",
		latestProgressR2.ProcessedKeys, latestProgressR2.ElapsedTime, finalRateR2)
	log.Printf("Total benchmark duration (including scan and tests): %s\n", time.Since(scanStartTime))
	log.Printf("-------------------------------\n")
}
