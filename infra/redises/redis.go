package redises

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"
	"os"

	"git.7k7k.com/data/abScheduler/infra/config"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/redis/go-redis/extra/redisprometheus/v9"
	"github.com/redis/go-redis/v9"
)

type ClientMgr struct {
	DefaultRedis   redis.UniversalClient // 默认。redlock、重定向任务cache、限流
	BaseRedis      redis.UniversalClient // 默认 v2。metric、白名单API
	ExpConfigRedis redis.UniversalClient // 实验配置
	UserStateRedis redis.UniversalClient // 用户分桶状态
	AdminRedis     redis.UniversalClient // admin 后台使用
	ChanRedis      redis.UniversalClient // notify 使用
	UserLabelRedis redis.UniversalClient // 用户标签
}

// @autowire(set=infra)
func NewClientMgr(conf *config.Config) *ClientMgr {
	redis.SetLogger(&logger{log.New(os.Stdout, "redis", log.Lshortfile|log.LstdFlags)})
	return &ClientMgr{
		DefaultRedis:   NewRedisFrom("default", conf, "default"),
		BaseRedis:      NewRedisFrom("base", conf, "default_v2"),
		ExpConfigRedis: NewRedisFrom("config", conf, "ab_config"),
		UserStateRedis: NewRedisFrom("user_state", conf, "user_state"),
		AdminRedis:     NewRedisFrom("admin", conf, "admin"),
		ChanRedis:      NewRedisFrom("chan", conf, "default_v2"),
		UserLabelRedis: NewRedisFrom("user_label", conf, "user_label"),
	}
}

func Use(cli redis.UniversalClient) *ClientMgr {
	return &ClientMgr{
		DefaultRedis:   cli,
		BaseRedis:      cli,
		ExpConfigRedis: cli,
		UserStateRedis: cli,
		UserLabelRedis: cli,
		AdminRedis:     cli,
		ChanRedis:      cli,
	}
}

// @autowire(set=infra)
func NewDefaultClient(clts *ClientMgr) redis.UniversalClient {
	return clts.DefaultRedis
}

func NewRedisFrom(name string, conf *config.Config, db string) redis.UniversalClient {
	return NewRedis(name, conf.DataBase.Redis[db])
}

func NewRedis(name string, c config.Redis, opts ...func(*redis.UniversalOptions)) redis.UniversalClient {
	options := &redis.UniversalOptions{
		ClientName: "abtest",
		Addrs:      []string{c.Addr},
		DB:         c.DB,
		Password:   c.Password,
	}
	if c.TLS {
		options.TLSConfig = &tls.Config{}
	}
	for _, opt := range opts {
		opt(options)
	}

	var cli redis.UniversalClient
	if c.Cluster {
		cli = redis.NewClusterClient(options.Cluster())
	} else {
		cli = redis.NewClient(options.Simple())
	}

	go func() {
		_, err := cli.Ping(context.Background()).Result()
		if err != nil {
			panic(err)
		}
	}()

	collector := redisprometheus.NewCollector("abtest", name, cli)
	prometheus.MustRegister(collector)

	return cli
}

type logger struct {
	log *log.Logger
}

func (l *logger) Printf(ctx context.Context, format string, v ...interface{}) {
	_ = l.log.Output(2, fmt.Sprintf(format, v...))
}
