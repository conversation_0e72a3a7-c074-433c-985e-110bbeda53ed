package redises

import (
	"context"
	"fmt"
	"log/slog"
	"runtime"
	"time"

	"git.7k7k.com/data/abScheduler/infra/metric"
	"git.7k7k.com/pkg/common/ctxs"
	"git.7k7k.com/pkg/common/gosentry"
	"git.7k7k.com/pkg/common/logs"
	"github.com/getsentry/sentry-go"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/redis/go-redis/v9"
)

// @autowire(set=infra)
func NewRedsync(rds redis.UniversalClient) *redsync.Redsync {
	return redsync.New(goredis.NewPool((redis.UniversalClient)(rds)))
}

func WithLock(rds redis.UniversalClient, lockKey string, duration time.Duration, fn func(context.Context) error) func() {
	return WithLockContext(context.Background(), rds, lockKey, duration, fn)
}

func WithLockContext(ctx0 context.Context, rds redis.UniversalClient, lockKey string, duration time.Duration, fn func(context.Context) error) func() {
	if runtime.GOOS == `darwin` {
		lockKey = lockKey + "." + runtime.GOOS
	}

	rlock := NewRedsync(rds)
	return func() {
		mutex := rlock.NewMutex(lockKey, redsync.WithExpiry(duration))
		ctx := context.WithValue(ctx0, ctxs.KeyTraceID, logs.GenerateTraceID())
		err := mutex.TryLockContext(ctx)
		if err != nil {
			slog.DebugContext(ctx, "[rlock] [lock-fail] "+lockKey, "error", err.Error())
			return
		}

		var finish func()
		ctx, finish = gosentry.InitTransaction(ctx, "cron:"+lockKey, "rlock.call", lockKey, lockKey)
		defer finish()

		slog.DebugContext(ctx, "[rlock] [lock-suc] "+lockKey)

		err = withTimer(lockKey, WithPanicRecover(fn))(ctx)
		if err != nil {
			slog.ErrorContext(ctx, "[rlock] [error] "+lockKey, "error", err.Error())
		}

		ok, err := mutex.Unlock()
		if !ok {
			slog.ErrorContext(ctx, "[rlock] [unlock-fail] "+lockKey, "error", err.Error())
			return
		}
		slog.DebugContext(ctx, "[rlock] [unlock-suc] "+lockKey)
	}
}

func WithPanicRecover(fn func(context.Context) error) func(context.Context) (err error) {
	return func(ctx context.Context) (err error) {
		defer func() {
			if r := recover(); r != nil {
				slog.ErrorContext(ctx, "WithPanicRecover", "err", r)
				err = fmt.Errorf("WithPanicRecover %+v", r)
				_ = sentry.RecoverWithContext(ctx)
			}
		}()

		return fn(ctx)
	}
}

func withTimer(name string, fn func(context.Context) error) func(context.Context) (err error) {
	return func(ctx context.Context) (err error) {
		beginAt := time.Now()
		err = fn(ctx)
		dur := time.Since(beginAt)
		if err == nil {
			slog.InfoContext(ctx, "WithTimer", "name", name, "duration", dur.String())
			metric.CronExecuteTime.WithLabelValues(name, "1").Observe(dur.Seconds())
		} else {
			slog.ErrorContext(ctx, "WithTimer", "name", name, "duration", dur.String(), "err", err.Error())
			metric.CronExecuteTime.WithLabelValues(name, "0").Observe(dur.Seconds())
		}
		return err
	}
}
