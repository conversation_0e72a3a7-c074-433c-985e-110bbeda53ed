package config

import (
	"os"

	"git.7k7k.com/pkg/common/gosentry"
	"github.com/BurntSushi/toml"
)

type Config struct {
	Mode     string
	Addr     string
	Sentry   gosentry.SentryConfig
	DataBase DataBase
	MQ       MQ
}

func (c *Config) IsProd() bool {
	return c.Mode == "prod"
}

type DataBase struct {
	MySQL   map[string]MySQL
	Redis   map[string]Redis
	MongoDB map[string]Mongo

	MySQLDefault string `toml:"mysql_default"`
}

type MySQL struct {
	DSN   string
	Debug bool
	Read  *MySQL
	Write *MySQL
}

type Redis struct {
	Addr     string
	Cluster  bool
	DB       int
	Password string
	TLS      bool `toml:"tls"`
}

type Mongo struct {
	URI        string
	DB         string
	Auth       string
	SecretName string `toml:"secret_name"`
}
type MQ struct {
	Kafka map[string]Kafka
}

type Kafka struct {
	Addrs []string
	SSL   bool
}

var glocalConfigPath string

func SetGlobalConfig(configPath string) {
	glocalConfigPath = configPath
}

// @autowire(set=infra)
func LoadGlobalConfig() (*Config, error) {
	return LoadConfig(glocalConfigPath)
}

func LoadConfig(configPath string) (*Config, error) {
	tomlData, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	c := &Config{}
	_, err = toml.Decode(string(tomlData), c) // 修改这里，添加下划线以忽略第一个返回值
	if err != nil {                           // 添加错误检查
		return nil, err
	}
	return c, nil
}
