package container

import (
	"reflect"
	"sync"
)

type Container struct {
	instances map[reflect.Type]any
	lock      sync.RWMutex
}

func (c *Container) RegisterInstance(v any) {
	c.lock.Lock()
	c.instances[reflect.TypeOf(v)] = v
	c.lock.Unlock()
}

func (c *Container) GetInstance() (v any) {
	c.lock.RLock()
	defer c.lock.RUnlock()
	return c.instances[reflect.TypeOf(v)]
}

var (
	defaultContainer = &Container{
		instances: make(map[reflect.Type]any, 8),
	}
)

func RegisterInstance(v any) {
	defaultContainer.RegisterInstance(v)
}

func GetInstance[T any]() (v T) {
	return defaultContainer.GetInstance().(T)
}
