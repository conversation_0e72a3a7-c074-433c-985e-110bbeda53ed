package metric

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var ( // Gauge
	AbStatsExpGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name: "ab_stats_exp_gauge",
		Help: "运行中实验个数",
	}, []string{"prj_id", "state"})

	AbStatsGroupGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name: "ab_stats_group_gauge",
		Help: "运行中实验组个数",
	}, []string{"prj_id", "state"})

	AbStatsExpUserCountGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name: "ab_stats_exp_user_count_gauge",
		Help: "实验用户数",
	}, []string{"prj_id", "exp_id"})

	RefreshTime = promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name: "ab_refresh_time",
		Help: "refresh 重载数据耗时",
	}, []string{"prj_id", "layer_id", "source"})

	CronExecuteTime = promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name: "cron_execute_time",
		Help: "定时任务",
	}, []string{"name", "suc"})

	ProjectDAU = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name: "ab_project_dau_gauge",
		Help: "DAU",
	}, []string{"prj_id", "country"})
)

var ( // Counter
	Request = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "ab_request_count",
		Help: "请求信息",
	}, []string{"prj_id", "country", "app_version"})

	Traffic = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "ab_traffic_count",
		Help: "请求粒度的分流结果",
	}, []string{"prj_id", "layer_id", "exp_id", "group_id"})

	TrafficV2 = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "ab_traffic_v2_count",
		Help: "分流结果",
	}, []string{"prj_id", "layer_id", "exp_id", "cache"})

	CacheRemoveCount = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "ab_cache_remove_count",
		Help: "缓存清理次数",
	}, []string{"prj_id", "layer_id", "exp_id"})

	CSRError = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "ab_csr_error_count",
		Help: "CSR解析错误",
	}, []string{"prj_id", "layer_id", "exp_id"})
)

var (
	QueueSize = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name: "ab_queue_size",
		Help: "当前队列中等待处理的任务数量",
	}, []string{"queue_name"})
)
