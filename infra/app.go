package infra

import (
	"log"
	"log/slog"
	"net/http"
	"os"
	"runtime"
	"time"

	"git.7k7k.com/data/abScheduler/httpd"
	"git.7k7k.com/data/abScheduler/infra/config"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/data/abScheduler/repository/mongos"
	"github.com/facebookgo/grace/gracehttp"
)

// @autowire(set=infra)
type Container struct {
	Config *config.Config
	Redis  *redises.ClientMgr
	HTTP   *http.Server

	httpd.Handlers

	MongoSet *mongos.MongoSet
}

type Application struct {
	*Container
}

// @autowire(set=infra)
func NewApplication(c *Container) (*Application, func(), error) {
	app := &Application{Container: c}
	return app, app.CleanUp, nil
}

func (app *Application) RunHTTP() {
	app.HTTP.Addr = app.Config.Addr
	addr := app.HTTP.Addr
	if runtime.GOOS == "darwin" {
		addr = "http://127.0.0.1" + addr
	}
	slog.Info("HTTP Server ListenAndServe", "addr", addr, "since", time.Since(Process.StartedAt).String(), "pid", os.Getpid())
	err := gracehttp.ServeWithOptions([]*http.Server{app.HTTP}, gracehttp.PreStartProcess(func() error {
		log.SetOutput(os.Stderr)
		return nil
	}))
	if err != nil && err != http.ErrServerClosed {
		log.Fatalln("HTTP Server ListenAndServe", err)
	}
	log.Println("http_shutdown", err)
}

func (app *Application) CleanUp() {
}

var (
	onStartFuncs []func() error
)

func OnStart(f func() error) {
	onStartFuncs = append(onStartFuncs, f)
}

func Start() (err error) {
	for _, f := range onStartFuncs {
		err = f()
		if err != nil {
			return
		}
	}
	return
}
