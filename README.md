# scheduler

## 开发

更新 gorm-gen

	go run ./dal/cmd

更新 autowire

	make wire

运行

```
make gorun	# go run
make watch	# 依赖 gowatch 监控本地文件变化
```

代码结构

```
- httpd						# http 接口适配，不要放业务逻辑
- service					# 业务逻辑
	- data_transfer.go		# 同步数据 admin-mysql -> redis
	- config_meta_store.go	# 实验原始数据维护
	- config_service.go		# 从 Mem/Redis 读取实验配置
	- user_state.go			# 用户状态维护：分流流程和重定向模块读写
	- admin_service.go		# 管理后台相关：需要改写后台状态的都收在这里
	- redirect_service.go	# 冻结重定向模块
- resource					# 资源文件，编译时会被打包到二进制里
- gopkg 					# 公共类库
- infra						# 项目基础设施
```
