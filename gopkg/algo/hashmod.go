package algo

import (
	"hash/fnv"
	"sync"

	"github.com/cockroachdb/errors"
)

const MaxSlotsCount = 10000

type HashMod struct {
	id         string
	totalSlots int // 总slot数量
	schemes    []Scheme
	slots      []Scheme // slot分布
	gcdWeight  int
	mux        sync.RWMutex
}

func NewHashMod(id string, schemes []Scheme) (*HashMod, error) {
	if len(schemes) == 0 {
		return nil, errors.New("no schemes provided")
	}
	hm := &HashMod{id: "hm_" + id, schemes: schemes}
	hm.assigneSlots()
	return hm, nil
}

func (hm *HashMod) assigneSlots() {
	hm.mux.Lock()
	defer hm.mux.Unlock()
	hm.gcdWeight = hm.getGCDWeight()
	hm.totalSlots = hm.getLCMWeight(hm.gcdWeight)
	slotIndex := 0
	totalWeight := 0
	for _, scheme := range hm.schemes {
		totalWeight += scheme.Weight / hm.gcdWeight
	}
	if totalWeight > hm.totalSlots {
		hm.totalSlots = totalWeight
	}
	slots := make([]Scheme, hm.totalSlots)
	for _, scheme := range hm.schemes {
		sCount := hm.totalSlots * (scheme.Weight / hm.gcdWeight) / totalWeight
		for j := 0; j < sCount; j++ {
			slots[slotIndex] = scheme
			slotIndex++
		}
	}
	hm.slots = slots
}

func (hm *HashMod) AssigneUser(uid uint64) Scheme {
	hm.mux.RLock()
	defer hm.mux.RUnlock()
	index := uid % uint64(hm.totalSlots)
	return hm.slots[index]
}

func (hm *HashMod) getGCDWeight() int {
	gcdWeight := hm.schemes[0].Weight
	for _, scheme := range hm.schemes {
		gcdWeight = gcd(scheme.Weight, gcdWeight)
	}
	return gcdWeight
}

func (hm *HashMod) getLCMWeight(gcdWeight int) int {
	lcmWeight := hm.schemes[0].Weight / gcdWeight
	for _, scheme := range hm.schemes {
		lcmWeight = lcm(lcmWeight, scheme.Weight/gcdWeight)
	}
	return lcmWeight
}

// gcd 最大公约数
func gcd(a, b int) int {
	for b != 0 {
		a, b = b, a%b
	}
	return a
}

// lcm 最小公倍数
func lcm(a, b int) int {
	return a * b / gcd(a, b)
}

func HashUid(uid string) uint64 {
	mHash := fnv.New64()
	_, _ = mHash.Write([]byte(uid))
	return mHash.Sum64()
}
