package algo

import (
	"github.com/google/uuid"
	"testing"
)

var schemes []Scheme

func init() {
	schemes = []Scheme{
		{
			Name:   "s1",
			Weight: 2,
		},
		{
			Name:   "s2",
			Weight: 1,
		},
		{
			Name:   "s3",
			Weight: 1,
		},
		{
			Name:   "s4",
			Weight: 1,
		},
		{
			Name:   "s5",
			Weight: 1,
		},
		{
			Name:   "s6",
			Weight: 94,
		},
	}
}

func TestNewHashMod(t *testing.T) {

	_, err := NewHashMod("test", schemes)
	if err != nil {
		t.Fatal(err)
	}

}

func BenchmarkNewHashMod(b *testing.B) {
	hashMod, err := NewHashMod("bench", schemes)
	if err != nil {
		b.<PERSON><PERSON>(err)
	}
	totalCount := 0
	stat := make(map[string]int)
	for i := 0; i < b.N; i++ {
		uid := uuid.NewString()
		scheme := hashMod.AssigneUser(HashUid(uid))
		stat[scheme.Name]++
		totalCount++
	}
	b.Logf("Total: %d\n", totalCount)
	for k, v := range stat {
		b.Logf("%s: %d, Ratio: %f\n", k, v, float64(v)/float64(totalCount))
	}
}
