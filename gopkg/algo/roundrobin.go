package algo

import (
	"sync"
	"time"

	"github.com/cockroachdb/errors"
)

// RoundRobin 轮询算法
type RoundRobin struct {
	*Algo
	currentIndex int
	mux          sync.Mutex
}

// NewRoundRobin 新建轮询算法
func NewRoundRobin(id string, schemes []Scheme) (*RoundRobin, error) {
	if len(schemes) == 0 {
		//sugared.Errorf("schemes list cannot be empty")
		return nil, errors.New("schemes list cannot be empty")
	}

	id = "rr_" + id
	currentIndex := 0
	if status := loadAlgoStatus(id); status != nil {
		currentIndex = status["currentIndex"]
	}
	return &RoundRobin{
		Algo:         NewAlgo(id, schemes),
		currentIndex: currentIndex,
	}, nil
}

//func (rr *RoundRobin) GetCreateTime() time.Time {
//	return rr.createTime
//}

// Next 获取下一个scheme
func (rr *RoundRobin) Next() Scheme {
	rr.mux.Lock()
	defer rr.mux.Unlock()
	if rr.schemeCount == 1 {
		return rr.schemes[0]
	}
	scheme := rr.schemes[rr.currentIndex]
	rr.currentIndex = (rr.currentIndex + 1) % rr.schemeCount
	rr.lastDistributeTime = time.Now()
	return scheme
}

func (rr *RoundRobin) Close() {
	rr.mux.Lock()
	defer rr.mux.Unlock()
	status := map[string]int{"currentIndex": rr.currentIndex}
	_ = saveAlgoStatus(rr.id, status)
}

func (rr *RoundRobin) ResetSchemes(schemes []Scheme) {
	rr.mux.Lock()
	defer rr.mux.Unlock()
	if rr.CheckReset(schemes, false) {
		rr.schemes = schemes
		rr.schemeCount = len(rr.schemes)
		if rr.currentIndex > rr.schemeCount {
			rr.currentIndex = 0
		}
	}
}
