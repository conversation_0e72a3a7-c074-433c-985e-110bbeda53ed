package algo

import (
	"time"

	"github.com/cockroachdb/errors"
)

// WeightedRoundRobin 加权轮训
type WeightedRoundRobin struct {
	*Algo
	currentIndex  int
	currentWeight int
	gcdWeight     int
	maxWeight     int
	//n             int
}

// NewWeightRoundRobin 新建加权轮训
func NewWeightRoundRobin(id string, schemes []Scheme) (*WeightedRoundRobin, error) {
	if len(schemes) == 0 {
		//sugared.Errorf("schems list cannot be empty")
		return nil, errors.New("schemes list cannot be empty")
	}
	id = "wrr_" + id
	currentIndex := -1
	currentWeight := 0
	if status := loadAlgoStatus(id); status != nil {
		currentIndex = status["currentIndex"]
		currentWeight = status["currentWeight"]
	}
	wrr := &WeightedRoundRobin{
		Algo:          NewAlgo(id, schemes),
		currentIndex:  currentIndex,
		currentWeight: currentWeight,
		//n:             len(schemes),
	}
	maxWeight := wrr.getMaxWeight(schemes)
	if maxWeight == 0 {
		//sugared.Errorf("all schemes have weight of 0")
		return nil, errors.New("all schemes have weight of 0")
	}
	wrr.maxWeight = maxWeight
	wrr.gcdWeight = wrr.getGCDWeight(schemes)
	return wrr, nil
}

// resetSchemes 重置方案列表 当前方案列表与提供方案列表不同的情况下重置
func (wrr *WeightedRoundRobin) resetSchemes(schemes []Scheme) {
	wrr.mux.Lock()
	defer wrr.mux.Unlock()

	if wrr.CheckReset(schemes, true) {
		//sugared.Infof("update schemes: %v", schemes)
		maxWeight := wrr.getMaxWeight(schemes)
		if maxWeight > 0 {
			wrr.schemes = schemes
			wrr.maxWeight = maxWeight
			wrr.gcdWeight = wrr.getGCDWeight(schemes)
			//wrr.n = len(schemes)
			wrr.schemeCount = len(wrr.schemes)
		}
	}
}

// Next 获取下一个要分发的方案
func (wrr *WeightedRoundRobin) Next() Scheme {
	wrr.mux.Lock()
	defer wrr.mux.Unlock()
	wrr.lastDistributeTime = time.Now()
	if len(wrr.schemes) == 1 {
		return wrr.schemes[0]
	}
	for {
		wrr.currentIndex = (wrr.currentIndex + 1) % wrr.schemeCount
		if wrr.currentIndex == 0 {
			wrr.currentWeight = wrr.currentWeight - wrr.gcdWeight
			if wrr.currentWeight <= 0 {
				wrr.currentWeight = wrr.maxWeight
				if wrr.currentWeight == 0 {
					return Scheme{}
				}
			}
		}
		if wrr.schemes[wrr.currentIndex].Weight >= wrr.currentWeight {
			return wrr.schemes[wrr.currentIndex]
		}
	}
}

// getMaxWeight 获取最大权重
func (wrr *WeightedRoundRobin) getMaxWeight(schemes []Scheme) int {
	maxWeight := 0
	for _, scheme := range schemes {
		if scheme.Weight > maxWeight {
			maxWeight = scheme.Weight
		}
	}
	return maxWeight
}

// getGCD 获取最大公约数
func (wrr *WeightedRoundRobin) getGCD(a, b int) int {
	for b == 0 {
		return a
	}
	return wrr.getGCD(b, a%b)
}

// getGCDWeight 获取权重最大公约数
func (wrr *WeightedRoundRobin) getGCDWeight(schemes []Scheme) int {
	if len(schemes) == 0 {
		return 1
	}
	gcdWeight := schemes[0].Weight
	for _, scheme := range schemes {
		gcdWeight = wrr.getGCD(gcdWeight, scheme.Weight)
	}
	return gcdWeight
}

func (wrr *WeightedRoundRobin) ResetSchemes(schemes []Scheme) {
	wrr.resetSchemes(schemes)
	return
}

func (wrr *WeightedRoundRobin) Close() {
	wrr.mux.Lock()
	defer wrr.mux.Unlock()
	//sugared.Infof("Close Weighted RoundRobin")
	status := map[string]int{
		"currentIndex":  wrr.currentIndex,
		"currentWeight": wrr.currentWeight,
	}
	_ = saveAlgoStatus(wrr.id, status)
}
