package algo

import (
	"math/rand"
	"sort"
	"time"

	"github.com/cockroachdb/errors"
)

type SudokuRoundRobin struct {
	*RoundRobin
	sequence       bool
	changeSequence bool
}

func NewSudokuRoundRobin(id string, schemes []Scheme) (*SudokuRoundRobin, error) {
	if len(schemes) == 0 {
		//sugared.Errorf("schemes list cannot be empty")
		return nil, errors.New("schemes list cannot be empty")
	}
	//取消随机，默认取第一个
	currentIndex := 0
	//currentIndex := rand.Intn(len(schemes))
	sequence := true
	changeSequence := false
	id = "sdk_rr_" + id
	if status := loadAlgoStatus(id); status != nil {
		currentIndex = status["currentIndex"]
		sequence = intToBool(status["sequence"])
		changeSequence = intToBool(status["changeSequence"])
	}
	rr, err := NewRoundRobin(id, schemes)
	if err != nil {
		return nil, err
	}
	rr.currentIndex = currentIndex
	return &SudokuRoundRobin{
		RoundRobin:     rr,
		sequence:       sequence,
		changeSequence: changeSequence,
	}, nil
}

//func (sdk *SudokuRoundRobin) ResetSchemes(schemes []Scheme) {
//	sdk.mux.Lock()
//	defer sdk.mux.Unlock()
//
//	if sdk.CheckReset(schemes, false) {
//		//RandomOrder(schemes)
//		sdk.schemes = schemes
//		sdk.currentIndex = 0
//		sdk.lastDistributeTime = time.Now()
//		sugared.Infof("SyhSchemes 数量方法变化: %v", schemes)
//		return
//	}
//}

func (sdk *SudokuRoundRobin) Next() Scheme {
	sdk.mux.Lock()
	defer sdk.mux.Unlock()
	if sdk.schemeCount == 1 {
		return sdk.schemes[0]
	}

	if sdk.schemeCount == 1 {
		return sdk.schemes[0]
	}
	scheme := sdk.schemes[sdk.currentIndex]
	sdk.currentIndex = (sdk.currentIndex + 1) % sdk.schemeCount
	sdk.lastDistributeTime = time.Now()
	if sdk.currentIndex == 0 {
		RandomOrder(sdk.schemes)
		//sugared.Infof("初始分配: key:%s, Schemes: %v", sdk.id, sdk.schemes)
	}
	return scheme
}

func (sdk *SudokuRoundRobin) Close() {
	sdk.mux.Lock()
	defer sdk.mux.Unlock()
	status := map[string]int{
		"currentIndex":   sdk.currentIndex,
		"sequence":       boolToInt(sdk.sequence),
		"changeSequence": boolToInt(sdk.changeSequence),
	}
	saveAlgoStatus(sdk.id, status)
}

// boolToInt 布尔值 true = 1， false = 0
func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

// intToBool 1 = true 否则为false
func intToBool(i int) bool {
	// if i == 1 {
	// 	return true
	// }
	// return false
	return i == 1
}

// RandomOrder 随机打乱顺序
func RandomOrder(schemes []Scheme) {
	rand.Shuffle(len(schemes), func(i, j int) {
		schemes[j], schemes[i] = schemes[i], schemes[j]
	})
}

// ExtractNames extracts the Name fields from a slice of Scheme
func ExtractNames(schemes []Scheme) []string {
	names := make([]string, len(schemes))
	for i, scheme := range schemes {
		names[i] = scheme.Name
	}
	sort.Strings(names)
	return names
}
