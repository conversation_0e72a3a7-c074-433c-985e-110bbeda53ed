package stat

import (
	"math/rand"
	"testing"
)

func TestChiSquareUniformity(t *testing.T) {
	// 生成两个随机序列
	seq1 := generateRandomSequence(1000, 10) // 1000个数，范围0-9
	seq2 := generateBiasedSequence(1000, 10) // 生成偏差较大的序列

	// 计算每个序列的卡方值
	chiSquare1 := calculateChiSquare(seq1, 10)
	chiSquare2 := calculateChiSquare(seq2, 10)

	t.Logf("序列1的卡方值: %.2f", chiSquare1)
	t.Logf("序列2的卡方值: %.2f", chiSquare2)

	// 添加频率分布的输出
	printDistribution(t, "序列1", seq1, 10)
	printDistribution(t, "序列2", seq2, 10)
	// 卡方值越小，分布越均匀
}

// 生成均匀分布的随机序列
func generateRandomSequence(size, bins int) []int {
	seq := make([]int, size)
	for i := 0; i < size; i++ {
		seq[i] = rand.Intn(bins)
	}
	return seq
}

// 生成有偏差的随机序列
func generateBiasedSequence(size, bins int) []int {
	seq := make([]int, size)
	for i := 0; i < size; i++ {
		// 使某些数字出现概率更高
		if rand.Float64() < 0.9 {
			seq[i] = rand.Intn(bins / 2)
		} else {
			seq[i] = rand.Intn(bins)
		}
	}
	return seq
}

// 计算卡方值
func calculateChiSquare(seq []int, bins int) float64 {
	// 统计每个数字出现的频率
	observed := make([]int, bins)
	for _, v := range seq {
		observed[v]++
	}

	// 计算期望频率
	expected := float64(len(seq)) / float64(bins)

	// 计算卡方值
	chiSquare := 0.0
	for _, o := range observed {
		chiSquare += (float64(o) - expected) * (float64(o) - expected) / expected
	}

	return chiSquare
}

func printDistribution(t *testing.T, name string, seq []int, bins int) {
	observed := make([]int, bins)
	for _, v := range seq {
		observed[v]++
	}

	t.Logf("\n%s的频率分布:", name)
	for i, count := range observed {
		t.Logf("数字 %d: %d 次", i, count)
	}
}
