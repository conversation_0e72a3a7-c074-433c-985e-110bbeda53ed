package stat

import (
	"math"

	"gonum.org/v1/gonum/stat/distuv"
)

// UniformityTest 进行均匀性检验
// 参数：
//   - bucketCounts: 每个桶的实际计数
//   - totalCount: 总样本数
//
// 返回值：
//   - chiSquare: 卡方统计量，衡量观察值与期望值的偏离程度，值越大表示偏离越大
//   - pValue: 在原假设（分布均匀）下，观察到当前或更极端结果的概率
//   - p值 > 0.05：不能拒绝原假设，认为分布是均匀的
//   - p值 < 0.05：拒绝原假设，认为分布不均匀
//   - cv: 变异系数（标准差/平均值），衡量数据的离散程度
//   - cv < 0.05：表示分布非常均匀
//   - cv > 0.05：表示分布波动较大
func UniformityTest[T int | float64](bucketCounts []T, totalCount int) (chiSquare, pValue, cv float64) {
	numBuckets := float64(len(bucketCounts))
	expectedCount := float64(totalCount) / numBuckets // 平均值

	// 卡方检验
	chiSquare = 0.0
	for _, observed := range bucketCounts {
		diff := float64(observed) - expectedCount
		chiSquare += (diff * diff) / expectedCount
	}

	// 计算p值
	df := numBuckets - 1 // 自由度
	dist := distuv.ChiSquared{K: df}
	pValue = 1 - dist.CDF(chiSquare)

	// 计算变异系数
	mean := float64(totalCount) / numBuckets
	variance := 0.0
	for _, count := range bucketCounts {
		diff := float64(count) - mean
		variance += diff * diff
	}
	variance /= numBuckets
	stdDev := math.Sqrt(variance)
	cv = stdDev / mean

	return
}
