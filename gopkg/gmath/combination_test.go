package gmath

import (
	"reflect"
	"sort"
	"strconv"
	"testing"
)

// 辅助函数：按照字典序排序结果，用于比较两个结果集是否相等
func sortCombinations(combinations [][]int) {
	sort.Slice(combinations, func(i, j int) bool {
		for k := 0; k < len(combinations[i]); k++ {
			if k >= len(combinations[j]) {
				return false
			}
			if combinations[i][k] != combinations[j][k] {
				return combinations[i][k] < combinations[j][k]
			}
		}
		return len(combinations[i]) < len(combinations[j])
	})
}

// 测试用例结构
type testCase struct {
	name     string
	input    []int
	expected [][]int
}

// 生成测试用例
func getTestCases() []testCase {
	return []testCase{
		{
			name:     "空输入",
			input:    []int{},
			expected: [][]int{},
		},
		{
			name:     "单元素",
			input:    []int{3},
			expected: [][]int{{1}, {2}, {3}},
		},
		{
			name:     "包含0的输入",
			input:    []int{2, 0, 3},
			expected: [][]int{},
		},
		{
			name:  "两个元素",
			input: []int{2, 3},
			expected: [][]int{
				{1, 1}, {1, 2}, {1, 3},
				{2, 1}, {2, 2}, {2, 3},
			},
		},
		{
			name:  "三个元素",
			input: []int{2, 2, 2},
			expected: [][]int{
				{1, 1, 1}, {1, 1, 2}, {1, 2, 1}, {1, 2, 2},
				{2, 1, 1}, {2, 1, 2}, {2, 2, 1}, {2, 2, 2},
			},
		},
		{
			name:  "不同大小的元素",
			input: []int{1, 2, 3},
			expected: [][]int{
				{1, 1, 1}, {1, 1, 2}, {1, 1, 3},
				{1, 2, 1}, {1, 2, 2}, {1, 2, 3},
			},
		},
	}
}

// 测试递归版本的 GenerateCombinations
func TestGenerateCombinations(t *testing.T) {
	testCases := getTestCases()

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := GenerateCombinations(tc.input)

			// 排序结果和期望值，以便比较
			sortCombinations(result)
			sortCombinations(tc.expected)

			if !reflect.DeepEqual(result, tc.expected) {
				t.Errorf("期望: %v, 得到: %v", tc.expected, result)
			}
		})
	}
}

// 测试迭代版本的 GenerateCombinationsIterative
func TestGenerateCombinationsIterative(t *testing.T) {
	testCases := getTestCases()

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := GenerateCombinationsIterative(tc.input)

			// 排序结果和期望值，以便比较
			sortCombinations(result)
			sortCombinations(tc.expected)

			if !reflect.DeepEqual(result, tc.expected) {
				t.Errorf("期望: %v, 得到: %v", tc.expected, result)
			}
		})
	}
}

// 测试两个函数的结果一致性
func TestConsistencyBetweenImplementations(t *testing.T) {
	testInputs := [][]int{
		{},
		{3},
		{2, 3},
		{2, 2, 2},
		{1, 2, 3},
		{3, 2, 4},
		{3, 2, 4, 10},
		{2, 0, 3},
	}

	for _, input := range testInputs {
		t.Run("输入: "+formatInput(input), func(t *testing.T) {
			recursiveResult := GenerateCombinations(input)
			iterativeResult := GenerateCombinationsIterative(input)

			// 排序结果以便比较
			sortCombinations(recursiveResult)
			sortCombinations(iterativeResult)

			if !reflect.DeepEqual(recursiveResult, iterativeResult) {
				t.Errorf("递归和迭代版本的结果不一致\n递归版本: %v\n迭代版本: %v",
					recursiveResult, iterativeResult)
			}
		})
	}
}

// 格式化输入，用于测试名称
func formatInput(input []int) string {
	if len(input) == 0 {
		return "空"
	}
	result := "["
	for i, v := range input {
		if i > 0 {
			result += ","
		}
		result += strconv.Itoa(v)
	}
	result += "]"
	return result
}

// 基准测试：小规模输入
func BenchmarkSmallInput(b *testing.B) {
	input := []int{2, 3}

	b.Run("递归版本", func(b *testing.B) {
		for b.Loop() {
			GenerateCombinations(input)
		}
	})

	b.Run("迭代版本", func(b *testing.B) {
		for b.Loop() {
			GenerateCombinationsIterative(input)
		}
	})
}

// 基准测试：中等规模输入
func BenchmarkMediumInput(b *testing.B) {
	input := []int{3, 3, 3}

	b.Run("递归版本", func(b *testing.B) {
		for b.Loop() {
			GenerateCombinations(input)
		}
	})

	b.Run("迭代版本", func(b *testing.B) {
		for b.Loop() {
			GenerateCombinationsIterative(input)
		}
	})
}

// 基准测试：较大规模输入
func BenchmarkLargeInput(b *testing.B) {
	input := []int{4, 4, 4, 4}

	b.Run("递归版本", func(b *testing.B) {
		for b.Loop() {
			GenerateCombinations(input)
		}
	})

	b.Run("迭代版本", func(b *testing.B) {
		for b.Loop() {
			GenerateCombinationsIterative(input)
		}
	})
}

// 基准测试：非等长输入
func BenchmarkVariedInput(b *testing.B) {
	input := []int{2, 3, 4, 5}

	b.Run("递归版本", func(b *testing.B) {
		for b.Loop() {
			GenerateCombinations(input)
		}
	})

	b.Run("迭代版本", func(b *testing.B) {
		for b.Loop() {
			GenerateCombinationsIterative(input)
		}
	})
}
