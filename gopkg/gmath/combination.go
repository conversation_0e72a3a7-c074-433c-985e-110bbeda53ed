package gmath

// GenerateCombinations 生成所有可能的组合
// 输入 nums []int，每个元素表示该位置的候选数量
// 输出 [][]int，表示所有可能的组合
// 例如：输入 []int{2,3} 输出 [][]int{{1,1}, {1,2}, {1,3}, {2,1}, {2,2}, {2,3}}
func GenerateCombinations(nums []int) [][]int {
	if len(nums) == 0 {
		return [][]int{}
	}

	// 初始化结果，存放第一个元素的所有可能值
	result := make([][]int, 0)
	for i := 1; i <= nums[0]; i++ {
		result = append(result, []int{i})
	}

	// 递归处理剩余元素
	for i := 1; i < len(nums); i++ {
		nextResult := make([][]int, 0)
		// 遍历当前已有的组合
		for _, combination := range result {
			// 对于每个组合，添加当前位置的所有可能值
			for j := 1; j <= nums[i]; j++ {
				newCombination := make([]int, len(combination))
				copy(newCombination, combination)
				newCombination = append(newCombination, j)
				nextResult = append(nextResult, newCombination)
			}
		}
		result = nextResult
	}

	return result
}

// GenerateCombinationsIterative 使用迭代方式生成所有可能的组合
// 对于较大的输入，这种方式可能比递归方式更高效
func GenerateCombinationsIterative(nums []int) [][]int {
	if len(nums) == 0 {
		return [][]int{}
	}

	// 计算组合总数
	total := 1
	for _, n := range nums {
		total *= n
	}

	result := make([][]int, total)
	for i := 0; i < total; i++ {
		result[i] = make([]int, len(nums))
	}

	// 填充结果数组
	factor := 1
	for i := len(nums) - 1; i >= 0; i-- {
		for j := 0; j < total; j++ {
			// 计算当前位置应该填入的值
			value := ((j / factor) % nums[i]) + 1
			result[j][i] = value
		}
		factor *= nums[i]
	}

	return result
}
