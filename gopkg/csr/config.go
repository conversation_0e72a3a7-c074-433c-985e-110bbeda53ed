package csr

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"git.7k7k.com/pkg/common/metric"
	"github.com/cockroachdb/errors"

	"github.com/redis/go-redis/v9"
)

type ApplyType string

const (
	ApplyTypeActive ApplyType = "active"
	ApplyTypeNew    ApplyType = "new"
)

const (
	IndentityKeyEcpm      string = "ecpm"
	IndentityKeyGameNum   string = "gamenum"
	IndentityKeyAdNum     string = "adnum"
	IndentityKeyADay      string = "aday" // 活跃天数
	IndentityKeyCountry   string = "country"
	IndentityKeyStartTime string = "start_time"
	IndentityKeyNetwork   string = "network"
)

type Configs map[ApplyType]Config

type Config struct {
	IndentityKeys    []string         `yaml:"indentityKeys" json:"indentityKeys"`
	IndentityConfigs IndentityConfigs `yaml:"indentityConfigs" json:"indentityConfigs"`
}

type IndentityConfig struct {
	Kind  string `yaml:"kind" json:"kind"`
	Value []any  `yaml:"value" json:"value"`
}

type IndentityConfigs map[string]IndentityConfig

type KVStruct struct {
	ActiveDays   int     `json:"activeDays"`
	AvgGameTime  float64 `json:"avgGameTime"`
	AvgAds       float64 `json:"avgAds"`
	AvgGames     float64 `json:"avgGames"`
	AvgAdRevenue float64 `json:"avgAdRevenue"`
	Ecpm         float64 `json:"eCPM"`
}

// GetUserLabelFromRedisForCSR 从 redis 里取出用户特征
func GetUserLabelFromRedisForCSR(ctx context.Context, csrRedis redis.UniversalClient, prefixKey, uid string) (map[string]any, KVStruct, error) {

	mtr := metric.NewDatabaseRequests("abtest", "")
	beginAt := time.Now()
	defer func() {
		mtr.Emit("RedisCSR", "Get", "0", time.Since(beginAt))
	}()

	key := prefixKey + uid
	resultStr, err := csrRedis.Get(ctx, key).Result()
	if err != nil {
		//sugared.Errorf("redisClient.Get(%q): %v", key, err)
		return nil, KVStruct{}, errors.WithStack(err)
	}
	cacheData := KVStruct{}
	err = json.Unmarshal([]byte(resultStr), &cacheData)
	if err != nil {
		//sugared.Errorf("json.Unmarshal(%s): %v", resultStr, err)
		return nil, KVStruct{}, errors.WithStack(err)
	}

	cacheDataMap := map[string]any{}
	_ = json.Unmarshal([]byte(resultStr), &cacheDataMap)

	return cacheDataMap, cacheData, nil
}

// ParseCsrIndentityFromCsrParams 根据 csrParams 解析出 csr 的 indentity
func ParseCsrIndentityFromCsrParams(csrConfig Config, csrParams map[string]any) (string, error) {
	indentity := ""
	for i, k := range csrConfig.IndentityKeys {
		paramValue, ok := csrParams[k]
		if !ok {
			return "", errors.New("csrParam: " + k + " not found")
		}
		kind := csrConfig.IndentityConfigs[k].Kind
		values := csrConfig.IndentityConfigs[k].Value
		if i != 0 {
			indentity += "_"
		}
		indentity += strconv.Itoa(ParseIndentity(kind, paramValue, values))
	}
	return indentity, nil
}

func ParseCsrIndentityFromCsrCacheData(csrConfig Config, cacheData KVStruct) (string, error) {
	indentity := ""
	for i, k := range csrConfig.IndentityKeys {
		paramValue, err := getCsrValueFromCacheData(k, cacheData)
		if err != nil {
			//sugared.Errorf("getCsrValueFromCacheData(%s): %v", k, err)
			return "", err
		}
		kind := csrConfig.IndentityConfigs[k].Kind
		values := csrConfig.IndentityConfigs[k].Value
		if i != 0 {
			indentity += "_"
		}
		indentity += strconv.Itoa(ParseIndentity(kind, paramValue, values))
	}
	return indentity, nil
}

func getCsrValueFromCacheData(indentityKey string, cacheData KVStruct) (any, error) {
	switch indentityKey {
	case IndentityKeyEcpm:
		return cacheData.Ecpm, nil
	case IndentityKeyAdNum:
		return cacheData.AvgAds, nil
	case IndentityKeyGameNum:
		return cacheData.AvgGames, nil
	case IndentityKeyADay:
		return cacheData.ActiveDays, nil
	default:
		return nil, errors.New("indentityKey: " + indentityKey + " not found")
	}
}
