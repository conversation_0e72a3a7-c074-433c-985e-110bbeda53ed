# Makefile

# 设置Go工具的路径，通常是GOROOT下的bin目录
GOBIN := $(shell go env GOPATH)/bin

# 设置wire工具的路径
WIRE := $(GOBIN)/gutowire

# 设置你的Go模块路径
MODULE := .

# GIT
GIT_HEAD := $(shell git rev-parse --short HEAD)

# 设置wire生成文件的路径，通常是wire_gen.go
WIRE_GEN_FILE := ./autowire/*.go

# 定义所有源文件，不包括wire_gen.go
SRC := $(shell find . -type f -name '*.go' -not -name 'wire_gen.go')

# 检查wire工具是否已安装
ifeq ($(shell which $(WIRE) 2>/dev/null),)
WIRE_INSTALLED := no
else
WIRE_INSTALLED := yes
endif

# 安装wire工具
.PHONY: install_wire
install_wire:
ifeq ($(WIRE_INSTALLED),no)
	go install github.com/google/wire/cmd/wire@v0.6.0
	go install github.com/micln/go-autowire/cmd/gutowire@v0.1.0
endif

# 构建你的项目
.PHONY: build
build: $(WIRE_GEN_FILE)
	go build -ldflags "-X 'main.GIT_HEAD=$(GIT_HEAD)'"

# go run
.PHONY: gorun
gorun: $(WIRE_GEN_FILE) # 请同时修改 watch
	go run . -c=resource/config/config_local.toml serve

# go run
.PHONY: watch
watch: $(WIRE_GEN_FILE)
	DISABLE_CRON=1 GIN_MODE=release $(GOBIN)/gowatch -args='-c=resource/config/config_local.toml,serve'

# 生成wire依赖注入代码
.PHONY: wire
wire: install_wire
	$(WIRE) -s . -w autowire

# 清理生成的文件
.PHONY: clean
clean:
	rm -rf $(WIRE_GEN_FILE)

# 设置默认目标
.DEFAULT_GOAL := all

# 定义all目标
.PHONY: all
all: build
