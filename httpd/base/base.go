package base

import (
	"git.7k7k.com/pkg/common/ctxs"
	"github.com/gin-gonic/gin"
)

type HTTPOut[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message,omitempty"`
	Data    T      `json:"data,omitempty"`
}

type HTTPOutAny struct {
	Code    int    `json:"code"`
	TraceId string `json:"trace_id"`
	Message string `json:"message,omitempty"`
	Data    any    `json:"data,omitempty"`
}

func ctxRender(gc *gin.Context) func(code int, obj any) {
	// if runtime.GOOS == "darwin" {
	// 	return gc.IndentedJSON
	// }
	return gc.JSON
}

func Write(gc *gin.Context, code int, data any) {
	ctxRender(gc)(200, HTTPOutAny{Code: code, Data: data, TraceId: ctxs.TraceId(gc)})
}

func WriteOk(gc *gin.Context, data any) {
	ctxRender(gc)(200, HTTPOutAny{Code: 0, Data: data, TraceId: ctxs.TraceId(gc)})
}

func WriteError(gc *gin.Context, code int, msg string) {
	ctxRender(gc)(200, HTTPOutAny{Code: code, Message: msg, TraceId: ctxs.TraceId(gc)})
}
