package httpd

import (
	"context"
	"time"

	"git.7k7k.com/data/abScheduler/httpd/base"
	"git.7k7k.com/data/abScheduler/service"
	"github.com/gin-gonic/gin"
)

// @autowire(set=http)
type HelloController struct {
	AbConfigReader *service.ConfigService
}

func (c *HelloController) Hello(gc *gin.Context) {
	base.WriteOk(gc, map[string]any{"hello": "scheduler", "time": time.Now()})
}

type HelloOut struct {
	Name string `json:"name"`
}

func (c *HelloController) Hello2(ctx context.Context, in struct{}) (HelloOut, error) {
	project, err := c.AbConfigReader.GetProject(ctx, 1, "")
	if err != nil {
		return HelloOut{}, err
	}

	return HelloOut{Name: project.Key}, nil
}
